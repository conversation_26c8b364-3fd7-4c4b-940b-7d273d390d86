using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Xunit;

namespace Winmug.Tests;

public class AuthenticationTests
{
    [Fact]
    public void OAuthCredentials_IsAuthenticated_ReturnsFalseWhenTokensAreNull()
    {
        // Arrange
        var credentials = new OAuthCredentials
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret"
        };

        // Act & Assert
        credentials.IsAuthenticated.Should().BeFalse();
    }

    [Fact]
    public void OAuthCredentials_IsAuthenticated_ReturnsTrueWhenTokensAreSet()
    {
        // Arrange
        var credentials = new OAuthCredentials
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret",
            AccessToken = "access-token",
            AccessTokenSecret = "access-token-secret"
        };

        // Act & Assert
        credentials.IsAuthenticated.Should().BeTrue();
    }

    [Fact]
    public void OAuthSignatureGenerator_GenerateNonce_ReturnsNonEmptyString()
    {
        // Act
        var nonce = OAuthSignatureGenerator.GenerateNonce();

        // Assert
        nonce.Should().NotBeNullOrEmpty();
        nonce.Length.Should().Be(32); // GUID without hyphens
    }

    [Fact]
    public void OAuthSignatureGenerator_GenerateTimestamp_ReturnsValidTimestamp()
    {
        // Act
        var timestamp = OAuthSignatureGenerator.GenerateTimestamp();

        // Assert
        timestamp.Should().NotBeNullOrEmpty();
        long.TryParse(timestamp, out _).Should().BeTrue();
    }

    [Fact]
    public void OAuthSignatureGenerator_UrlEncode_EncodesSpecialCharacters()
    {
        // Arrange
        var input = "hello world@#$^&";

        // Act
        var encoded = OAuthSignatureGenerator.UrlEncode(input);

        // Assert
        encoded.Should().NotContain(" ", "spaces should be encoded as %20");
        encoded.Should().NotContain("@", "@ should be encoded");
        encoded.Should().NotContain("#", "# should be encoded");
        encoded.Should().NotContain("$", "$ should be encoded");
        encoded.Should().NotContain("^", "^ should be encoded");
        encoded.Should().NotContain("&", "& should be encoded");
        encoded.Should().Contain("%20", "spaces should become %20");
        encoded.Should().Contain("%40", "@ should become %40");
    }

    [Fact]
    public void SmugMugAuthenticationService_IsAuthenticated_ReturnsFalseInitially()
    {
        // Arrange
        var httpClient = new HttpClient();
        var credentialStorage = new Mock<ISecureCredentialStorage>();
        var options = Options.Create(new SmugMugOAuthOptions
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret"
        });
        var logger = new Mock<ILogger<SmugMugAuthenticationService>>();

        var authService = new SmugMugAuthenticationService(httpClient, credentialStorage.Object, options, logger.Object);

        // Act & Assert
        authService.IsAuthenticated.Should().BeFalse();
    }

    [Fact]
    public void SmugMugOAuthOptions_DefaultValues_RequestFullAccess()
    {
        // Arrange & Act
        var options = new SmugMugOAuthOptions();

        // Assert
        options.Access.Should().Be("Full", "because we need full access to download private photos");
        options.Permissions.Should().Be("Read", "because we only need read access to download photos");
        options.CallbackUrl.Should().Be("oob", "because desktop apps use out-of-band callback");
    }

    [Fact]
    public void SmugMugAuthenticationService_InitiateAuthentication_IncludesFullAccessInUrl()
    {
        // Arrange
        var httpClient = new HttpClient();
        var credentialStorage = new Mock<ISecureCredentialStorage>();
        var options = Options.Create(new SmugMugOAuthOptions
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret",
            Access = "Full",
            Permissions = "Read"
        });
        var logger = new Mock<ILogger<SmugMugAuthenticationService>>();

        var authService = new SmugMugAuthenticationService(httpClient, credentialStorage.Object, options, logger.Object);

        // Note: This test would require mocking HTTP responses to work properly
        // For now, we're just testing the configuration

        // Act & Assert
        options.Value.Access.Should().Be("Full");
        options.Value.Permissions.Should().Be("Read");
    }
}
