using System.Text.Json;
using Microsoft.Extensions.Logging;
using Winmug.Core.Authentication;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Improved SmugMug API client following best practices from API documentation
/// </summary>
public class ImprovedSmugMugApiClient : ISmugMugApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ILogger<ImprovedSmugMugApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";

    // URI Cache to store all URIs from the authuser response for efficient navigation
    private SmugMugUris? _cachedUserUris;
    private string? _cachedRootNodeId;
    private DateTime? _uriCacheTime;

    public ImprovedSmugMugApiClient(
        HttpClient httpClient,
        ISmugMugAuthenticationService authService,
        ILogger<ImprovedSmugMugApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    /// <summary>
    /// Cache URIs from the authuser response for efficient navigation throughout the session
    /// This implements the pattern described in SmugMugAPI.md to cache all URIs from /api/v2!authuser
    /// </summary>
    private void CacheUserUris(SmugMugUser user)
    {
        if (user.Uris != null)
        {
            _cachedUserUris = user.Uris;
            _uriCacheTime = DateTime.UtcNow;

            // Extract and cache the root node ID from the Node URI
            if (user.Uris.Node?.Uri != null)
            {
                var nodeUri = user.Uris.Node.Uri;
                _cachedRootNodeId = nodeUri.Split('/').LastOrDefault();
                _logger.LogInformation("🗂️ Cached user URIs and root node ID: {NodeId}", _cachedRootNodeId);
                _logger.LogDebug("📋 Available cached URIs: Node, Folder, UserAlbums, UserRecentImages, and {UriCount} others",
                    CountAvailableUris(user.Uris));
            }
        }
    }

    /// <summary>
    /// Count available URIs for logging purposes
    /// </summary>
    private int CountAvailableUris(SmugMugUris uris)
    {
        var count = 0;
        var properties = typeof(SmugMugUris).GetProperties();
        foreach (var prop in properties)
        {
            if (prop.GetValue(uris) is SmugMugUriInfo uriInfo && !string.IsNullOrEmpty(uriInfo.Uri))
            {
                count++;
            }
        }
        return count;
    }

    /// <summary>
    /// Clear the URI cache (call when user logs out or session ends)
    /// </summary>
    public void ClearUriCache()
    {
        _cachedUserUris = null;
        _cachedRootNodeId = null;
        _uriCacheTime = null;
        _logger.LogInformation("🗑️ Cleared URI cache");
    }

    /// <summary>
    /// Get a cached URI for efficient navigation
    /// </summary>
    private string? GetCachedUri(Func<SmugMugUris, SmugMugUriInfo?> uriSelector)
    {
        if (_cachedUserUris == null)
        {
            _logger.LogDebug("No cached URIs available - need to call GetAuthenticatedUserAsync first");
            return null;
        }

        var uriInfo = uriSelector(_cachedUserUris);
        return uriInfo?.Uri;
    }

    /// <summary>
    /// Get the cached root node ID for efficient access
    /// </summary>
    private string? GetCachedRootNodeId()
    {
        if (string.IsNullOrEmpty(_cachedRootNodeId))
        {
            _logger.LogDebug("No cached root node ID available - need to call GetAuthenticatedUserAsync first");
            return null;
        }

        return _cachedRootNodeId;
    }

    /// <summary>
    /// Get all user albums using cached URI for efficient access
    /// </summary>
    public async Task<List<SmugMugAlbum>> GetAllUserAlbumsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting all user albums using cached URI...");

        // Try to use cached UserAlbums URI first
        var userAlbumsUri = GetCachedUri(uris => uris.UserAlbums);

        if (userAlbumsUri != null)
        {
            _logger.LogDebug("Using cached UserAlbums URI: {Uri}", userAlbumsUri);
            var albums = new List<SmugMugAlbum>();

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(userAlbumsUri, cancellationToken))
            {
                albums.Add(album);
            }

            _logger.LogInformation("Found {AlbumCount} albums using cached URI", albums.Count);
            return albums;
        }

        // Fallback: get user and use UserAlbums URI
        _logger.LogDebug("No cached URI available, getting user albums via authuser");
        var user = Task.Run(async () => await GetAuthenticatedUserAsync(cancellationToken)).GetAwaiter().GetResult();

        if (user.Uris?.UserAlbums?.Uri != null)
        {
            var albums = new List<SmugMugAlbum>();

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(user.Uris.UserAlbums.Uri, cancellationToken))
            {
                albums.Add(album);
            }

            _logger.LogInformation("Found {AlbumCount} albums using fallback method", albums.Count);
            return albums;
        }

        throw new InvalidOperationException("Unable to access user albums - no UserAlbums URI available");
    }

    /// <summary>
    /// Gets the authenticated user information - SYNCHRONOUS VERSION
    /// </summary>
    public SmugMugUser GetAuthenticatedUser(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting authenticated user information from /api/v2!authuser...");

        var url = $"{BaseApiUrl}!authuser";
        _logger.LogDebug("Making authenticated request to: {Url}", url);

        // Make synchronous request
        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, url);
        request.Headers.Add("Accept", "application/json");

        _logger.LogInformation("Making authenticated request to: {Url}", url);

        var httpResponse = _httpClient.Send(request, cancellationToken);
        var rawContent = Task.Run(async () => await httpResponse.Content.ReadAsStringAsync(cancellationToken)).GetAwaiter().GetResult();

        _logger.LogInformation("Raw API Response Status: {StatusCode} {ReasonPhrase}", httpResponse.StatusCode, httpResponse.ReasonPhrase);
        _logger.LogInformation("Raw API Response Content Length: {Length}", rawContent.Length);

        if (!httpResponse.IsSuccessStatusCode)
        {
            _logger.LogError("API request failed with status {StatusCode}: {Content}", httpResponse.StatusCode, rawContent);
            throw new InvalidOperationException($"Failed to get authenticated user information: {httpResponse.StatusCode} - {rawContent}");
        }

        var response = Task.Run(async () => await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, url, cancellationToken)).GetAwaiter().GetResult();

        // LOG COMPLETE RAW RESPONSE FOR DEBUGGING
        _logger.LogInformation("=== COMPLETE AUTHUSER RESPONSE DEBUG ===");
        try
        {
            var responseJson = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null // Keep original property names
            });
            _logger.LogInformation("📋 FULL AUTHUSER RESPONSE JSON:");
            _logger.LogInformation("{ResponseJson}", responseJson);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "❌ Failed to serialize response for logging");
        }
        _logger.LogInformation("=== END AUTHUSER RESPONSE DEBUG ===");

        if (response?.Response?.User == null)
        {
            _logger.LogError("❌ CRITICAL: Failed to get authenticated user information");
            _logger.LogError("   Response is null: {ResponseNull}", response == null);
            _logger.LogError("   Response.Response is null: {ResponseResponseNull}", response?.Response == null);
            _logger.LogError("   Response.Response.User is null: {UserNull}", response?.Response?.User == null);
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        var user = response.Response.User;

        // Enhanced logging to capture comprehensive user information
        _logger.LogInformation("📋 COMPREHENSIVE USER INFORMATION:");
        _logger.LogInformation("  Name: '{Name}'", user.Name ?? "NULL");
        _logger.LogInformation("  NickName: '{NickName}'", user.NickName ?? "NULL");
        _logger.LogInformation("  FirstName: '{FirstName}'", user.FirstName ?? "NULL");
        _logger.LogInformation("  LastName: '{LastName}'", user.LastName ?? "NULL");
        _logger.LogInformation("  Email: '{Email}'", user.Email ?? "NULL");
        _logger.LogInformation("  Plan: '{Plan}'", user.Plan ?? "NULL");
        _logger.LogInformation("  AccountStatus: '{AccountStatus}'", user.AccountStatus ?? "NULL");
        _logger.LogInformation("  ImageCount: {ImageCount}", user.ImageCount ?? 0);
        _logger.LogInformation("  WebUri: '{WebUri}'", user.WebUri ?? "NULL");
        _logger.LogInformation("  Uri: '{Uri}'", user.Uri ?? "NULL");
        _logger.LogInformation("  Has Uris: {HasUris}", user.Uris != null);

        // Log Node ID extraction from URI
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("  ✅ NODE ID EXTRACTED: {NodeId}", user.NodeId);
        }
        else
        {
            _logger.LogWarning("  ❌ NO NODE ID FOUND - This indicates limited access permissions!");
        }

        // Log all available URIs from the response
        if (user.Uris != null)
        {
            _logger.LogInformation("📋 AVAILABLE URIs:");
            if (user.Uris.Node?.Uri != null)
                _logger.LogInformation("  ✅ Node URI: '{NodeUri}' -> Node ID: {NodeId}", user.Uris.Node.Uri, user.NodeId);
            if (user.Uris.Folder?.Uri != null)
                _logger.LogInformation("  ✅ Folder URI: '{FolderUri}'", user.Uris.Folder.Uri);
            if (user.Uris.UserAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserAlbums URI: '{UserAlbumsUri}'", user.Uris.UserAlbums.Uri);
            if (user.Uris.UserRecentImages?.Uri != null)
                _logger.LogInformation("  ✅ UserRecentImages URI: '{UserRecentImagesUri}'", user.Uris.UserRecentImages.Uri);
            if (user.Uris.UserFeaturedAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserFeaturedAlbums URI: '{UserFeaturedAlbumsUri}'", user.Uris.UserFeaturedAlbums.Uri);
        }
        else
        {
            _logger.LogWarning("  ❌ NO URIs FOUND - This indicates limited access permissions!");
        }

        _logger.LogInformation("Successfully retrieved user: {UserName} ({NickName})",
            user.EffectiveName, user.EffectiveNickName);

        // CRITICAL: Cache all URIs from the authuser response for efficient navigation
        CacheUserUris(user);

        return user;
    }

    /// <summary>
    /// Gets the authenticated user information - ASYNC VERSION
    /// </summary>
    public async Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting authenticated user information from /api/v2!authuser...");

        var url = $"{BaseApiUrl}!authuser";
        _logger.LogDebug("Making authenticated request to: {Url}", url);

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, url, cancellationToken);

        // LOG COMPLETE RAW RESPONSE FOR DEBUGGING
        _logger.LogInformation("=== COMPLETE AUTHUSER RESPONSE DEBUG ===");
        try
        {
            var responseJson = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null // Keep original property names
            });
            _logger.LogInformation("📋 FULL AUTHUSER RESPONSE JSON:");
            _logger.LogInformation("{ResponseJson}", responseJson);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "❌ Failed to serialize response for logging");
        }
        _logger.LogInformation("=== END AUTHUSER RESPONSE DEBUG ===");

        if (response?.Response?.User == null)
        {
            _logger.LogError("❌ CRITICAL: Failed to get authenticated user information");
            _logger.LogError("   Response is null: {ResponseNull}", response == null);
            _logger.LogError("   Response.Response is null: {ResponseResponseNull}", response?.Response == null);
            _logger.LogError("   Response.Response.User is null: {UserNull}", response?.Response?.User == null);
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        var user = response.Response.User;

        // Enhanced logging to capture comprehensive user information
        _logger.LogInformation("📋 COMPREHENSIVE USER INFORMATION:");
        _logger.LogInformation("  Name: '{Name}'", user.Name ?? "NULL");
        _logger.LogInformation("  NickName: '{NickName}'", user.NickName ?? "NULL");
        _logger.LogInformation("  FirstName: '{FirstName}'", user.FirstName ?? "NULL");
        _logger.LogInformation("  LastName: '{LastName}'", user.LastName ?? "NULL");
        _logger.LogInformation("  Email: '{Email}'", user.Email ?? "NULL");
        _logger.LogInformation("  Plan: '{Plan}'", user.Plan ?? "NULL");
        _logger.LogInformation("  AccountStatus: '{AccountStatus}'", user.AccountStatus ?? "NULL");
        _logger.LogInformation("  ImageCount: {ImageCount}", user.ImageCount ?? 0);
        _logger.LogInformation("  WebUri: '{WebUri}'", user.WebUri ?? "NULL");
        _logger.LogInformation("  Uri: '{Uri}'", user.Uri ?? "NULL");
        _logger.LogInformation("  Has Uris: {HasUris}", user.Uris != null);

        // Log Node ID extraction from URI
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("  ✅ NODE ID EXTRACTED: {NodeId}", user.NodeId);
        }
        else
        {
            _logger.LogWarning("  ❌ NO NODE ID FOUND - This indicates limited access permissions!");
        }

        // Log all available URIs from the response
        if (user.Uris != null)
        {
            _logger.LogInformation("📋 AVAILABLE URIs:");
            if (user.Uris.Node?.Uri != null)
                _logger.LogInformation("  ✅ Node URI: '{NodeUri}' -> Node ID: {NodeId}", user.Uris.Node.Uri, user.NodeId);
            if (user.Uris.Folder?.Uri != null)
                _logger.LogInformation("  ✅ Folder URI: '{FolderUri}'", user.Uris.Folder.Uri);
            if (user.Uris.UserAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserAlbums URI: '{UserAlbumsUri}'", user.Uris.UserAlbums.Uri);
            if (user.Uris.UserRecentImages?.Uri != null)
                _logger.LogInformation("  ✅ UserRecentImages URI: '{UserRecentImagesUri}'", user.Uris.UserRecentImages.Uri);
            if (user.Uris.UserFeaturedAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserFeaturedAlbums URI: '{UserFeaturedAlbumsUri}'", user.Uris.UserFeaturedAlbums.Uri);
        }
        else
        {
            _logger.LogWarning("  ❌ NO URIs FOUND - This indicates limited access permissions!");
        }

        _logger.LogInformation("Successfully retrieved user: {UserName} ({NickName})",
            user.EffectiveName, user.EffectiveNickName);

        // CRITICAL: Cache all URIs from the authuser response for efficient navigation
        CacheUserUris(user);

        return user;
    }

    /// <summary>
    /// Gets the user's root node using cached Node ID for efficient access
    /// </summary>
    public async Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user root node using cached Node ID...");

        // Step 1: Try to use cached root node ID first
        var cachedNodeId = GetCachedRootNodeId();
        if (!string.IsNullOrEmpty(cachedNodeId))
        {
            _logger.LogInformation("✅ Using cached root node ID: {NodeId}", cachedNodeId);
            try
            {
                var rootNode = await GetNodeAsync(cachedNodeId, cancellationToken);
                _logger.LogInformation("✅ Successfully retrieved root node from cache: {NodeName} (ID: {NodeId}, Type: {Type})",
                    rootNode.Name, rootNode.NodeId, rootNode.Type);
                return rootNode;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get node using cached Node ID {NodeId}, refreshing cache", cachedNodeId);
            }
        }

        // Step 2: Get authenticated user to retrieve and cache Node ID
        var user = await GetAuthenticatedUserAsync(cancellationToken);
        _logger.LogDebug("User data retrieved: {UserName} ({NickName}) with Node ID: {NodeId}",
            user.Name, user.NickName, user.NodeId);

        // Step 3: Use the Node ID directly from the authuser response
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("✅ Using Node ID from authuser response: {NodeId}", user.NodeId);
            try
            {
                // Get the node details using the Node ID
                var rootNode = await GetNodeAsync(user.NodeId, cancellationToken);

                _logger.LogInformation("✅ Successfully retrieved root node: {NodeName} (ID: {NodeId}, Type: {Type})",
                    rootNode.Name, rootNode.NodeId, rootNode.Type);

                return rootNode;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get node using Node ID {NodeId}, trying fallback methods", user.NodeId);
            }
        }

        // Step 3: Fallback to Node URI approach if Node ID didn't work
        if (user.Uris?.Node?.Uri != null)
        {
            _logger.LogDebug("Fallback: Using Node URI from user response: {NodeUri}", user.Uris.Node.Uri);
            try
            {
                var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, user.Uris.Node.Uri, cancellationToken);

                if (response?.Response != null)
                {
                    _logger.LogInformation("✅ Successfully retrieved root node via Node URI: {NodeName} (ID: {NodeId}, Type: {Type})",
                        response.Response.Name, response.Response.NodeId, response.Response.Type);

                    return response.Response;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Node URI approach failed, trying fallback methods");
            }
        }
        else
        {
            _logger.LogWarning("No Node URI found in user response - this indicates limited OAuth permissions");
        }

        // Step 4: Try fallback approaches for limited access scenarios
        return await TryFallbackRootNodeApproaches(user, cancellationToken);
    }

    /// <summary>
    /// Try various fallback approaches to get the root node when primary method fails
    /// </summary>
    private async Task<SmugMugNode> TryFallbackRootNodeApproaches(SmugMugUser user, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Trying fallback approaches for root node access...");

        // Approach 1: Try !siteuser endpoint (alternative authenticated user endpoint)
        try
        {
            _logger.LogDebug("Fallback 1: Trying !siteuser endpoint");
            var siteUserUrl = $"{BaseApiUrl}!siteuser";

            // Try the nested structure first (like !authuser)
            try
            {
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.User?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.User.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (nested): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
            catch
            {
                // Try direct structure if nested fails
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (direct): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1 (!siteuser) failed");
        }

        // Approach 1b: Try direct authuser!node endpoint
        try
        {
            _logger.LogDebug("Fallback 1b: Trying !authuser!node");
            var nodeUrl = $"{BaseApiUrl}!authuser!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via authuser!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1b failed");
        }

        // Approach 2: Try user-specific node endpoint
        try
        {
            _logger.LogDebug("Fallback 2: Trying /user/{nickname}!node", user.NickName);
            var nodeUrl = $"{BaseApiUrl}/user/{user.NickName}!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via user!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 2 failed");
        }

        // Approach 3: Try to get user with Full response level
        try
        {
            _logger.LogDebug("Fallback 3: Trying user with Full response level");
            var fullUserUrl = $"{BaseApiUrl}/user/{user.NickName}?_responseLevel=Full";
            var fullUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, fullUserUrl, cancellationToken);

            if (fullUserResponse?.Response?.Uris?.Node?.Uri != null)
            {
                var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, fullUserResponse.Response.Uris.Node.Uri, cancellationToken);
                if (nodeResponse?.Response != null)
                {
                    _logger.LogInformation("✓ Root node found via Full response level: {NodeName} (ID: {NodeId})",
                        nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                    return nodeResponse.Response;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 3 failed");
        }

        // Approach 4: Create virtual root from albums (last resort)
        try
        {
            _logger.LogDebug("Fallback 4: Creating virtual root from accessible albums");
            var albumsUrl = $"{BaseApiUrl}/user/{user.NickName}!albums";
            var albumsResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<SmugMugAlbum>>>(HttpMethod.Get, albumsUrl, cancellationToken);

            if (albumsResponse?.Response != null && albumsResponse.Response.Any())
            {
                _logger.LogInformation("✓ Creating virtual root node - found {AlbumCount} accessible albums", albumsResponse.Response.Count);

                // Create a virtual root node since we can access albums but not the folder structure
                var virtualRoot = new SmugMugNode
                {
                    NodeId = "virtual-root",
                    Name = $"{user.Name}'s Photos",
                    Type = "Folder",
                    Description = "Virtual root - albums accessible with current permissions",
                    DateAdded = DateTime.Now,
                    DateModified = DateTime.Now,
                    HasChildren = true
                };

                return virtualRoot;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 4 failed");
        }

        // All approaches failed - provide detailed error information
        var errorMessage = "❌ UNABLE TO ACCESS FOLDER STRUCTURE\n\n" +
                          "This indicates insufficient OAuth permissions. The application needs 'Full' access to retrieve your folder structure.\n\n" +
                          "To fix this:\n" +
                          "1. Click 'Logout' to clear current credentials\n" +
                          "2. Click 'Authenticate with SmugMug' to start fresh\n" +
                          "3. When the SmugMug authorization page opens, make sure to:\n" +
                          "   - Click 'Allow' or 'Authorize' when prompted\n" +
                          "   - Grant full access permissions\n" +
                          "   - Do not select 'Public access only'\n\n" +
                          "If you continue to have issues, your SmugMug account may have restrictions that prevent folder structure access.";

        _logger.LogError(errorMessage);
        throw new InvalidOperationException(errorMessage);
    }

    /// <summary>
    /// Gets a specific node by its ID
    /// </summary>
    public async Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting node: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get node: {nodeId}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets the child nodes of a specific node using the correct API approach
    /// Uses the Node ID directly from the authuser response without _expand parameter
    /// </summary>
    public async Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting child nodes for: {NodeId} using direct Node API approach", nodeId);

        // CRITICAL: Check if nodeId is null or empty before constructing URL
        if (string.IsNullOrEmpty(nodeId))
        {
            _logger.LogError("❌ CRITICAL ERROR: Node ID is null or empty in GetChildNodesAsync!");
            _logger.LogError("   This indicates that the user's Node ID was not extracted properly from the authuser response");
            _logger.LogError("   This usually means insufficient OAuth permissions or authentication issues");
            throw new InvalidOperationException("Cannot get child nodes: Node ID is null or empty. This indicates insufficient OAuth permissions. Please re-authenticate with full access.");
        }

        // First, get the node details to access the ChildNodes URI from the response
        var nodeInfo = await GetNodeAsync(nodeId, cancellationToken);

        // Check if the node has a ChildNodes URI in its response
        if (nodeInfo.Uris?.ChildNodes?.Uri != null)
        {
            _logger.LogDebug("Using ChildNodes URI from node response: {Uri}", nodeInfo.Uris.ChildNodes.Uri);
            var nodes = new List<SmugMugNode>();

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(nodeInfo.Uris.ChildNodes.Uri, cancellationToken))
            {
                nodes.Add(node);
            }

            _logger.LogDebug("Found {Count} child nodes for: {NodeId} using URI navigation", nodes.Count, nodeId);
            return nodes;
        }
        else
        {
            // Fallback to constructed URL if no ChildNodes URI is available
            var url = $"{BaseApiUrl}/node/{nodeId}!children";
            _logger.LogDebug("No ChildNodes URI found, using constructed URL: {Url}", url);

            var nodes = new List<SmugMugNode>();

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(url, cancellationToken))
            {
                nodes.Add(node);
            }

            _logger.LogDebug("Found {Count} child nodes for: {NodeId} using constructed URL", nodes.Count, nodeId);
            return nodes;
        }
    }

    /// <summary>
    /// Gets all child nodes recursively for a given node
    /// </summary>
    public async Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all child nodes recursively for: {NodeId}", nodeId);
        
        var allNodes = new List<SmugMugNode>();
        var nodesToProcess = new Queue<string>();
        nodesToProcess.Enqueue(nodeId);

        while (nodesToProcess.Count > 0)
        {
            var currentNodeId = nodesToProcess.Dequeue();
            var childNodes = await GetChildNodesAsync(currentNodeId, cancellationToken);
            
            foreach (var childNode in childNodes)
            {
                allNodes.Add(childNode);
                
                // If it's a folder, add it to the queue for recursive processing
                if (childNode.IsFolder)
                {
                    nodesToProcess.Enqueue(childNode.NodeId);
                }
            }
        }

        _logger.LogDebug("Found {Count} total child nodes recursively for: {NodeId}", allNodes.Count, nodeId);
        return allNodes;
    }

    /// <summary>
    /// SIMPLE AND EFFICIENT: Get ALL albums using ONLY the UserAlbums endpoint
    /// This replaces all complex folder traversal logic with a single API call to /api/v2/user/{nickname}!albums
    /// Based on the JSON structure provided by the user - much simpler and more reliable
    /// SYNCHRONOUS VERSION - No async calls
    /// </summary>
    public FolderNode GetFolderStructure(CancellationToken cancellationToken = default)
    {
        // Use Task.Run to avoid deadlocks when calling async from sync context
        return Task.Run(async () => await GetFolderStructureAsync(cancellationToken)).GetAwaiter().GetResult();
    }

    /// <summary>
    /// ASYNC VERSION: Get ALL albums using ONLY the UserAlbums endpoint
    /// This is the proper async implementation that prevents deadlocks
    /// </summary>
    public async Task<FolderNode> GetFolderStructureAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🚀 STARTING ASYNC ALBUM DISCOVERY...");
        _logger.LogInformation("🎯 Goal: Get ALL albums directly using UserAlbums endpoint");
        _logger.LogInformation("📋 Using /api/v2/user/{{nickname}}!albums - the simplest and most reliable approach");

        var startTime = DateTime.Now;

        try
        {
            // Step 1: Get authenticated user information
            _logger.LogInformation("📡 Step 1: Getting authenticated user information...");
            var user = await GetAuthenticatedUserAsync(cancellationToken);

            if (user == null)
            {
                throw new InvalidOperationException("Failed to get authenticated user information");
            }

            _logger.LogInformation("✅ User authenticated: {UserName} (nickname: {NickName})", user.Name, user.NickName);

            // Step 2: Use the simple UserAlbums endpoint to get ALL albums
            var userAlbumsUri = $"{BaseApiUrl}/user/{user.NickName}!albums";
            _logger.LogInformation("📡 Step 2: Calling UserAlbums endpoint: {UserAlbumsUri}", userAlbumsUri);

            // DEBUG: Let's see what the actual JSON response looks like
            _logger.LogInformation("🔍 DEBUG: About to make first request to see JSON structure...");
            try
            {
                var debugRequest = _authService.CreateAuthenticatedRequest(HttpMethod.Get, userAlbumsUri);
                debugRequest.Headers.Add("Accept", "application/json");
                var debugResponse = await _httpClient.SendAsync(debugRequest, cancellationToken);
                var debugContent = await debugResponse.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogInformation("🔍 DEBUG: Raw JSON response (first 1000 chars): {JsonContent}",
                    debugContent.Length > 1000 ? debugContent.Substring(0, 1000) + "..." : debugContent);
            }
            catch (Exception debugEx)
            {
                _logger.LogWarning(debugEx, "Failed to get debug response");
            }

            var folderStructure = await BuildFolderStructureFromUserAlbumsSimpleAsync(userAlbumsUri, user.NickName, cancellationToken);

            var elapsed = DateTime.Now - startTime;
            _logger.LogInformation("✅ ALBUM DISCOVERY COMPLETE!");
            _logger.LogInformation("⏱️ Total time: {ElapsedTime:mm\\:ss}", elapsed);

            var totalFolders = CountTotalFolders(folderStructure);
            var totalAlbums = CountTotalAlbums(folderStructure);

            _logger.LogInformation("📊 Final Results:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbums);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", folderStructure.TotalImageCount);
            _logger.LogInformation("   💾 Estimated size: {Size}", FormatBytes(folderStructure.TotalEstimatedSizeBytes));

            return folderStructure;
        }
        catch (Exception ex)
        {
            var errorElapsed = DateTime.Now - startTime;
            _logger.LogError(ex, "❌ Failed to retrieve albums after {ElapsedTime:mm\\:ss}", errorElapsed);
            throw new InvalidOperationException($"Failed to retrieve albums: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Log available cached URIs for debugging
    /// </summary>
    private void LogAvailableUris()
    {
        if (_cachedUserUris == null) return;

        var uriCount = 0;
        if (_cachedUserUris.Node?.Uri != null)
        {
            _logger.LogInformation("  ✅ Node URI: {NodeUri}", _cachedUserUris.Node.Uri);
            uriCount++;
        }
        if (_cachedUserUris.Folder?.Uri != null)
        {
            _logger.LogInformation("  ✅ Folder URI: {FolderUri}", _cachedUserUris.Folder.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserAlbums?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserAlbums URI: {UserAlbumsUri}", _cachedUserUris.UserAlbums.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserRecentImages?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserRecentImages URI: {UserRecentImagesUri}", _cachedUserUris.UserRecentImages.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserProfile?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserProfile URI: {UserProfileUri}", _cachedUserUris.UserProfile.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserImageSearch?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserImageSearch URI: {UserImageSearchUri}", _cachedUserUris.UserImageSearch.Uri);
            uriCount++;
        }

        _logger.LogInformation("📋 Total cached URIs: {UriCount}", uriCount);

        // Log ALL available URIs for debugging large accounts
        _logger.LogInformation("🔍 DEBUGGING: Checking for additional URIs that might help with large accounts...");
        LogAllAvailableUrisForDebugging();
    }

    /// <summary>
    /// Log all available URIs for debugging purposes
    /// </summary>
    private void LogAllAvailableUrisForDebugging()
    {
        if (_cachedUserUris == null) return;

        _logger.LogInformation("🔍 ALL AVAILABLE URIs from /api/v2!authuser:");

        var uriProperties = typeof(SmugMugUris).GetProperties()
            .Where(p => p.PropertyType == typeof(SmugMugUriInfo))
            .ToList();

        foreach (var prop in uriProperties)
        {
            var uriRef = prop.GetValue(_cachedUserUris) as SmugMugUriInfo;
            if (uriRef?.Uri != null)
            {
                _logger.LogInformation("  🔗 {PropertyName}: {Uri}", prop.Name, uriRef.Uri);
            }
        }
    }

    /// <summary>
    /// Extract node ID from a Node URI (e.g., "/api/v2/node/ABC123" -> "ABC123")
    /// </summary>
    private string? ExtractNodeIdFromUri(string nodeUri)
    {
        if (string.IsNullOrEmpty(nodeUri)) return null;

        var parts = nodeUri.Split('/');
        return parts.Length > 0 ? parts.Last() : null;
    }

    /// <summary>
    /// Build optimized folder structure using cached Node URI and COMPLETE recursive traversal
    /// ENHANCED: Ensures ALL folders and albums are discovered regardless of HasChildren flags
    /// This is the primary method that implements SmugMugAPI.md best practices with comprehensive discovery
    /// </summary>
    private async Task<FolderNode> BuildOptimizedFolderStructureAsync(string rootNodeId, string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🌳 Building COMPREHENSIVE folder structure from root node: {NodeId}", rootNodeId);
        _logger.LogInformation("🎯 Goal: Discover ALL folders and albums in the entire hierarchy");

        try
        {
            // Get the root node details
            var rootNode = await GetNodeAsync(rootNodeId, cancellationToken);
            _logger.LogInformation("✅ Root node retrieved: {NodeName} (Type: {Type}, HasChildren: {HasChildren})",
                rootNode.Name, rootNode.Type, rootNode.HasChildren);

            // Create the root folder node
            var folderStructure = new FolderNode
            {
                NodeId = rootNode.NodeId,
                Name = rootNode.Name ?? $"{userNickname}'s Photos",
                Description = rootNode.Description ?? "Root folder",
                Type = rootNode.Type,
                UrlName = rootNode.UrlName ?? "",
                FullPath = "",
                DateCreated = rootNode.DateAdded ?? DateTime.MinValue,
                DateModified = rootNode.DateModified ?? DateTime.MinValue,
                HasChildren = rootNode.HasChildren ?? false
            };

            // CRITICAL: ALWAYS attempt comprehensive traversal regardless of HasChildren flag
            // The HasChildren flag is often unreliable, so we must check every node
            _logger.LogInformation("🔍 Starting COMPREHENSIVE traversal (ignoring HasChildren={HasChildren} flag)", rootNode.HasChildren);

            // First, try to get direct albums for the root
            await TryAddDirectAlbumsToFolderAsync(folderStructure, rootNodeId, cancellationToken);

            // Then, recursively traverse ALL child nodes
            await BuildFolderStructureRecursivelyOptimizedAsync(folderStructure, rootNodeId, "", cancellationToken);

            // Log comprehensive results
            var totalFolders = CountTotalFolders(folderStructure);
            var totalAlbums = CountTotalAlbums(folderStructure);

            _logger.LogInformation("✅ COMPREHENSIVE folder structure complete!");
            _logger.LogInformation("📊 FINAL RESULTS:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbums);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", folderStructure.TotalImageCount);
            _logger.LogInformation("   💾 Estimated size: {Size}", FormatBytes(folderStructure.TotalEstimatedSizeBytes));

            return folderStructure;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build comprehensive folder structure from node {NodeId}", rootNodeId);
            throw;
        }
    }

    /// <summary>
    /// Count total folders in the hierarchy (for reporting) - excludes root folder
    /// </summary>
    private int CountTotalFolders(FolderNode folder)
    {
        var count = folder.Children.Count; // Count direct child folders
        foreach (var child in folder.Children)
        {
            count += CountTotalFolders(child); // Count subfolders recursively
        }
        return count;
    }

    /// <summary>
    /// Count total albums in the hierarchy (for reporting)
    /// </summary>
    private int CountTotalAlbums(FolderNode folder)
    {
        var count = folder.Albums.Count; // Count albums in this folder
        foreach (var child in folder.Children)
        {
            count += CountTotalAlbums(child);
        }
        return count;
    }

    /// <summary>
    /// Build folder structure using SUPER EFFICIENT !folderlist endpoint
    /// This gets ALL folders in one API call, then populates albums for each
    /// Based on SmugMug API documentation: /api/v2/folder/user/{nickname}/{folder}!folderlist
    /// </summary>
    private async Task<FolderNode> BuildFolderStructureUsingFolderListAsync(string folderUri, string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🚀 Building folder structure using SUPER EFFICIENT !folderlist endpoint");
        _logger.LogInformation("📡 Folder URI: {FolderUri}", folderUri);

        try
        {
            // Construct the !folderlist URL from the cached folder URI
            var folderListUri = folderUri.TrimEnd('/') + "!folderlist";
            _logger.LogInformation("🔍 Calling !folderlist endpoint: {FolderListUri}", folderListUri);

            // Call the !folderlist endpoint to get ALL folders in one call
            var response = await SendAuthenticatedRequestAsync<SmugMugFolderListResponse>(HttpMethod.Get, folderListUri, cancellationToken);

            if (response?.FolderList == null || response.FolderList.Count == 0)
            {
                _logger.LogWarning("❌ !folderlist endpoint returned no folders");
                throw new InvalidOperationException("No folders returned from !folderlist endpoint");
            }

            _logger.LogInformation("✅ !folderlist returned {FolderCount} folders", response.FolderList.Count);

            // Create root folder structure
            var rootFolder = new FolderNode
            {
                NodeId = "folderlist-root",
                Name = $"{userNickname}'s Photos",
                Description = "Root folder from !folderlist",
                Type = "Folder",
                FullPath = "",
                DateCreated = DateTime.Now,
                DateModified = DateTime.Now,
                HasChildren = response.FolderList.Count > 0
            };

            // Build folder hierarchy from the flat list
            var folderMap = new Dictionary<string, FolderNode>();
            folderMap[""] = rootFolder; // Root folder with empty path

            // First pass: Create all folder nodes
            foreach (var folderInfo in response.FolderList)
            {
                _logger.LogDebug("📁 Processing folder: {FolderName} at path: {UrlPath}", folderInfo.Name, folderInfo.UrlPath);

                var folderNode = new FolderNode
                {
                    NodeId = ExtractNodeIdFromUri(folderInfo.Uri) ?? $"folder-{folderInfo.Name}",
                    Name = folderInfo.Name,
                    Description = $"Folder at {folderInfo.UrlPath}",
                    Type = "Folder",
                    FullPath = folderInfo.UrlPath?.TrimStart('/') ?? "",
                    DateCreated = DateTime.Now,
                    DateModified = DateTime.Now,
                    HasChildren = folderInfo.CanHaveFolder
                };

                folderMap[folderNode.FullPath] = folderNode;
            }

            // Second pass: Build hierarchy by connecting parent-child relationships
            foreach (var folderInfo in response.FolderList)
            {
                var folderPath = folderInfo.UrlPath?.TrimStart('/') ?? "";
                var folderNode = folderMap[folderPath];

                // Find parent folder
                var parentPath = GetParentPath(folderPath);
                if (folderMap.TryGetValue(parentPath, out var parentFolder))
                {
                    parentFolder.Children.Add(folderNode);
                    _logger.LogDebug("🔗 Connected {FolderName} to parent {ParentName}", folderNode.Name, parentFolder.Name);
                }
                else
                {
                    // If no specific parent found, add to root
                    rootFolder.Children.Add(folderNode);
                    _logger.LogDebug("🔗 Connected {FolderName} to root", folderNode.Name);
                }
            }

            // Third pass: Populate albums for each folder
            _logger.LogInformation("📸 Starting album discovery for all {FolderCount} folders...", response.FolderList.Count);
            var albumDiscoveryTasks = new List<Task>();

            foreach (var folderInfo in response.FolderList)
            {
                var folderPath = folderInfo.UrlPath?.TrimStart('/') ?? "";
                if (folderMap.TryGetValue(folderPath, out var folderNode))
                {
                    // Discover albums for this folder
                    albumDiscoveryTasks.Add(DiscoverAlbumsForFolderAsync(folderNode, folderInfo.Uri, cancellationToken));
                }
            }

            // Wait for all album discovery to complete
            await Task.WhenAll(albumDiscoveryTasks);

            // Calculate totals
            CalculateFolderTotals(rootFolder);

            var totalFolders = CountTotalFolders(rootFolder);
            var totalAlbums = CountTotalAlbums(rootFolder);

            _logger.LogInformation("✅ SUPER EFFICIENT folder structure complete!");
            _logger.LogInformation("📊 RESULTS from !folderlist:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbums);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", rootFolder.TotalImageCount);
            _logger.LogInformation("   💾 Estimated size: {Size}", FormatBytes(rootFolder.TotalEstimatedSizeBytes));

            return rootFolder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build folder structure using !folderlist endpoint");
            throw;
        }
    }

    /// <summary>
    /// Get parent path from a folder path (e.g., "SmugMug/Testing/New-stuff" -> "SmugMug/Testing")
    /// </summary>
    private string GetParentPath(string folderPath)
    {
        if (string.IsNullOrEmpty(folderPath))
            return "";

        var lastSlashIndex = folderPath.LastIndexOf('/');
        return lastSlashIndex > 0 ? folderPath.Substring(0, lastSlashIndex) : "";
    }

    /// <summary>
    /// Get all paged results synchronously
    /// </summary>
    private List<T> GetPagedResults<T>(string url, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 Starting paginated request to: {Url}", url);
        var allItems = new List<T>();
        var currentUrl = url;
        var pageNumber = 1;

        while (!string.IsNullOrEmpty(currentUrl))
        {
            _logger.LogInformation("📄 Fetching page {PageNumber}: {Url}", pageNumber, currentUrl);

            try
            {
                // Use Task.Run to avoid deadlocks when calling async from sync context
                var response = Task.Run(async () => await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugCollectionResponse<T>>>(HttpMethod.Get, currentUrl, cancellationToken)).GetAwaiter().GetResult();

                if (response?.Response != null)
                {
                    var items = ExtractItemsFromCollectionResponse<T>(response.Response);
                    allItems.AddRange(items);

                    _logger.LogInformation("✅ Page {PageNumber} fetched: {ItemCount} items (Total so far: {TotalCount})",
                        pageNumber, items.Count, allItems.Count);

                    // Log pagination info
                    if (response.Response.Pages != null)
                    {
                        _logger.LogInformation("📊 Pagination info - Count: {Count}, Start: {Start}, Total: {Total}",
                            response.Response.Pages.Count, response.Response.Pages.Start, response.Response.Pages.Total);

                        if (!string.IsNullOrEmpty(response.Response.Pages.NextPage))
                        {
                            _logger.LogInformation("➡️ Next page URL: {NextPage}", response.Response.Pages.NextPage);
                        }
                        else
                        {
                            _logger.LogInformation("🏁 No more pages - pagination complete");
                        }
                    }

                    // Get next page URL if available
                    currentUrl = response.Response.Pages?.NextPage;
                    pageNumber++;
                }
                else
                {
                    _logger.LogWarning("❌ Page {PageNumber} returned null response", pageNumber);
                    break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Failed to fetch page {PageNumber}: {Url}", pageNumber, currentUrl);
                throw;
            }
        }

        _logger.LogInformation("✅ Pagination complete! Total items fetched: {TotalCount} across {PageCount} pages",
            allItems.Count, pageNumber - 1);
        return allItems;
    }

    /// <summary>
    /// Gets all user albums using /api/v2/user/{nickname}!albums endpoint - SYNCHRONOUS VERSION
    /// </summary>
    public List<SmugMugAlbum> GetAllUserAlbums(CancellationToken cancellationToken = default)
    {
        var user = Task.Run(async () => await GetAuthenticatedUserAsync(cancellationToken)).GetAwaiter().GetResult();
        var userAlbumsUri = $"{BaseApiUrl}/user/{user.NickName}!albums";
        return GetPagedResults<SmugMugAlbum>(userAlbumsUri, cancellationToken);
    }

    /// <summary>
    /// Gets all images in a specific album - SYNCHRONOUS VERSION
    /// </summary>
    public List<SmugMugImage> GetAlbumImages(string albumKey, CancellationToken cancellationToken = default)
    {
        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        return GetPagedResults<SmugMugImage>(url, cancellationToken);
    }

    /// <summary>
    /// Downloads image data from the specified URL - SYNCHRONOUS VERSION
    /// </summary>
    public Stream DownloadImage(string imageUrl, CancellationToken cancellationToken = default)
    {
        return DownloadImage(imageUrl, null, cancellationToken);
    }

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting - SYNCHRONOUS VERSION
    /// </summary>
    public Stream DownloadImage(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, imageUrl);
        var response = _httpClient.Send(request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            throw new InvalidOperationException($"Failed to download image: {response.StatusCode}");
        }

        return response.Content.ReadAsStream(cancellationToken);
    }

    /// <summary>
    /// SIMPLE AND EFFICIENT: Build folder structure using ONLY the UserAlbums endpoint
    /// This is the new simplified approach that gets ALL albums directly from /api/v2/user/{nickname}!albums
    /// Based on the JSON structure provided by the user - SYNCHRONOUS VERSION
    /// </summary>
    private FolderNode BuildFolderStructureFromUserAlbumsSimple(string userAlbumsUri, string userNickname, CancellationToken cancellationToken)
    {
        return BuildFolderStructureFromUserAlbumsSimpleWithProgress(userAlbumsUri, userNickname, null, cancellationToken);
    }

    /// <summary>
    /// ASYNC VERSION: Build folder structure using ONLY the UserAlbums endpoint
    /// This prevents deadlocks by using proper async/await throughout
    /// </summary>
    private async Task<FolderNode> BuildFolderStructureFromUserAlbumsSimpleAsync(string userAlbumsUri, string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🚀 ASYNC SIMPLE APPROACH: Building folder structure using ONLY UserAlbums endpoint");
        _logger.LogInformation("📡 UserAlbumsUri: {UserAlbumsUri}", userAlbumsUri);

        try
        {
            // Create root folder structure
            var rootFolder = new FolderNode
            {
                NodeId = "useralbums-simple-root",
                Name = $"{userNickname}'s Photos",
                Description = "Root folder from UserAlbums (Simple Async)",
                Type = "Folder",
                FullPath = "",
                DateCreated = DateTime.Now,
                DateModified = DateTime.Now,
                HasChildren = true
            };

            var folderMap = new Dictionary<string, FolderNode>();
            folderMap[""] = rootFolder; // Root folder with empty path

            var albumCount = 0;
            var totalImages = 0;
            var totalSize = 0L;

            _logger.LogInformation("📡 Starting async page-by-page album fetching...");

            // Process albums page by page using proper async
            var currentUrl = userAlbumsUri;
            var pageNumber = 1;

            while (!string.IsNullOrEmpty(currentUrl))
            {
                _logger.LogInformation("📄 Processing page {PageNumber}: {Url}", pageNumber, currentUrl);

                // Get one page at a time using proper async
                // The user albums endpoint returns a direct array, so we use a special response type
                var response = await SendAuthenticatedRequestAsync<SmugMugUserAlbumsApiResponse>(HttpMethod.Get, currentUrl, cancellationToken);

                if (response?.Response == null)
                {
                    _logger.LogWarning("❌ Page {PageNumber} returned null response", pageNumber);
                    break;
                }

                var albums = response.Response;
                _logger.LogInformation("✅ Page {PageNumber} fetched: {ItemCount} albums", pageNumber, albums.Count);

                // Process each album in this page
                foreach (var album in albums)
                {
                    albumCount++;

                    try
                    {
                        // Log sample data for first few albums
                        if (albumCount <= 3)
                        {
                            _logger.LogInformation("📋 SAMPLE ALBUM DATA #{AlbumNumber}:", albumCount);
                            _logger.LogInformation("   Name: {Name}", album.Name);
                            _logger.LogInformation("   UrlPath: {UrlPath}", album.UrlPath ?? "null");
                            _logger.LogInformation("   ImageCount: {ImageCount}", album.ImageCount);
                            _logger.LogInformation("   TotalSizes: {TotalSizes} bytes", album.TotalSizes);
                        }

                        // Extract folder path from UrlPath
                        var folderPath = "";
                        if (!string.IsNullOrEmpty(album.UrlPath))
                        {
                            var lastSlashIndex = album.UrlPath.LastIndexOf('/');
                            if (lastSlashIndex > 0)
                            {
                                folderPath = album.UrlPath.Substring(1, lastSlashIndex - 1);
                            }
                        }

                        // Create album info - ONLY album details, no photo downloads
                        var actualSizeBytes = album.TotalSizes ?? 0L;
                        var imageCount = album.ImageCount ?? 0;

                        var albumInfo = new AlbumInfo
                        {
                            AlbumKey = album.AlbumKey,
                            NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                            Name = album.Name,
                            Description = album.Description ?? "",
                            UrlName = album.UrlName ?? "",
                            FullPath = album.UrlPath?.TrimStart('/') ?? album.Name,
                            ImageCount = imageCount,
                            EstimatedSizeBytes = actualSizeBytes,
                            DateCreated = album.Date ?? DateTime.MinValue,
                            DateModified = album.LastUpdated ?? DateTime.MinValue,
                            AllowDownloads = album.AllowDownloads ?? true,
                            Privacy = album.Privacy ?? "Unknown",
                            IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                        };

                        totalImages += imageCount;
                        totalSize += actualSizeBytes;

                        // Add to folder structure
                        var targetFolder = EnsureFolderExists(folderMap, folderPath, userNickname);
                        targetFolder.Albums.Add(albumInfo);

                        // Log progress every 50 albums
                        if (albumCount % 50 == 0)
                        {
                            _logger.LogInformation("📊 Progress: {AlbumCount} albums processed...", albumCount);
                        }
                    }
                    catch (Exception albumEx)
                    {
                        _logger.LogWarning(albumEx, "❌ Failed to process album: {AlbumName}", album.Name);
                    }
                }

                _logger.LogInformation("✅ Page {PageNumber} complete: {AlbumCount} total albums processed", pageNumber, albumCount);

                // Check for next page
                currentUrl = response.Pages?.NextPage;
                if (!string.IsNullOrEmpty(currentUrl))
                {
                    _logger.LogInformation("➡️ Next page URL: {NextPage}", currentUrl);
                    pageNumber++;
                }
                else
                {
                    _logger.LogInformation("🏁 No more pages - pagination complete");
                    break;
                }
            }

            // Calculate totals for all folders
            CalculateFolderTotals(rootFolder);

            var totalFolders = CountTotalFolders(rootFolder);
            var totalAlbumsInStructure = CountTotalAlbums(rootFolder);

            _logger.LogInformation("✅ ASYNC SIMPLE UserAlbums approach complete!");
            _logger.LogInformation("📊 FINAL RESULTS:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbumsInStructure);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", rootFolder.TotalImageCount);
            _logger.LogInformation("   💾 Total size: {Size}", FormatBytes(rootFolder.TotalEstimatedSizeBytes));

            return rootFolder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build folder structure from UserAlbums (Async Simple)");
            throw;
        }
    }

    /// <summary>
    /// SIMPLE AND EFFICIENT: Build folder structure using ONLY the UserAlbums endpoint WITH PROGRESS REPORTING
    /// This version supports progress callbacks for UI updates and processes pages one at a time
    /// </summary>
    private FolderNode BuildFolderStructureFromUserAlbumsSimpleWithProgress(string userAlbumsUri, string userNickname, Action<int, string>? progressCallback, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🚀 SIMPLE APPROACH: Building folder structure using ONLY UserAlbums endpoint");
        _logger.LogInformation("📡 UserAlbums URI: {UserAlbumsUri}", userAlbumsUri);

        try
        {
            // Create root folder structure
            var rootFolder = new FolderNode
            {
                NodeId = "useralbums-simple-root",
                Name = $"{userNickname}'s Photos",
                Description = "Root folder from UserAlbums (Simple)",
                Type = "Folder",
                FullPath = "",
                DateCreated = DateTime.Now,
                DateModified = DateTime.Now,
                HasChildren = true
            };

            var folderMap = new Dictionary<string, FolderNode>();
            folderMap[""] = rootFolder; // Root folder with empty path

            var albumCount = 0;
            var totalImages = 0;
            var totalSize = 0L;

            _logger.LogInformation("📡 Starting page-by-page album fetching...");
            progressCallback?.Invoke(0, "Starting album discovery...");

            // Process albums page by page
            var currentUrl = userAlbumsUri;
            var pageNumber = 1;

            while (!string.IsNullOrEmpty(currentUrl))
            {
                _logger.LogInformation("📄 Processing page {PageNumber}: {Url}", pageNumber, currentUrl);
                progressCallback?.Invoke(albumCount, $"Fetching page {pageNumber}...");

                // Get one page at a time using Task.Run to avoid deadlocks
                var response = Task.Run(async () => await SendAuthenticatedRequestAsync<SmugMugUserAlbumsApiResponse>(HttpMethod.Get, currentUrl, cancellationToken)).GetAwaiter().GetResult();

                if (response?.Response == null)
                {
                    _logger.LogWarning("❌ Page {PageNumber} returned null response", pageNumber);
                    break;
                }

                var albums = response.Response;
                _logger.LogInformation("✅ Page {PageNumber} fetched: {ItemCount} albums", pageNumber, albums.Count);

                // Process each album in this page
                foreach (var album in albums)
                {
                    albumCount++;

                    try
                    {
                        // Log sample data for first few albums
                        if (albumCount <= 3)
                        {
                            _logger.LogInformation("📋 SAMPLE ALBUM DATA #{AlbumNumber}:", albumCount);
                            _logger.LogInformation("   Name: {Name}", album.Name);
                            _logger.LogInformation("   UrlPath: {UrlPath}", album.UrlPath ?? "null");
                            _logger.LogInformation("   ImageCount: {ImageCount}", album.ImageCount);
                            _logger.LogInformation("   TotalSizes: {TotalSizes} bytes", album.TotalSizes);
                        }

                        // Extract folder path from UrlPath
                        var folderPath = "";
                        if (!string.IsNullOrEmpty(album.UrlPath))
                        {
                            var lastSlashIndex = album.UrlPath.LastIndexOf('/');
                            if (lastSlashIndex > 0)
                            {
                                folderPath = album.UrlPath.Substring(1, lastSlashIndex - 1);
                            }
                        }

                        // Create album info - ONLY album details, no photo downloads
                        var actualSizeBytes = album.TotalSizes ?? 0L;
                        var imageCount = album.ImageCount ?? 0;

                        var albumInfo = new AlbumInfo
                        {
                            AlbumKey = album.AlbumKey,
                            NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                            Name = album.Name,
                            Description = album.Description ?? "",
                            UrlName = album.UrlName ?? "",
                            FullPath = album.UrlPath?.TrimStart('/') ?? album.Name,
                            ImageCount = imageCount,
                            EstimatedSizeBytes = actualSizeBytes,
                            DateCreated = album.Date ?? DateTime.MinValue,
                            DateModified = album.LastUpdated ?? DateTime.MinValue,
                            AllowDownloads = album.AllowDownloads ?? true,
                            Privacy = album.Privacy ?? "Unknown",
                            IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                        };

                        totalImages += imageCount;
                        totalSize += actualSizeBytes;

                        // Add to folder structure
                        var targetFolder = EnsureFolderExists(folderMap, folderPath, userNickname);
                        targetFolder.Albums.Add(albumInfo);

                        // Report progress every 10 albums
                        if (albumCount % 10 == 0)
                        {
                            progressCallback?.Invoke(albumCount, $"Processed {albumCount} albums...");
                        }
                    }
                    catch (Exception albumEx)
                    {
                        _logger.LogWarning(albumEx, "❌ Failed to process album: {AlbumName}", album.Name);
                    }
                }

                _logger.LogInformation("✅ Page {PageNumber} complete: {AlbumCount} total albums processed", pageNumber, albumCount);
                progressCallback?.Invoke(albumCount, $"Page {pageNumber} complete - {albumCount} albums total");

                // Check for next page
                currentUrl = response.Pages?.NextPage;
                if (!string.IsNullOrEmpty(currentUrl))
                {
                    _logger.LogInformation("➡️ Next page URL: {NextPage}", currentUrl);
                    pageNumber++;
                }
                else
                {
                    _logger.LogInformation("🏁 No more pages - pagination complete");
                    break;
                }
            }

            // Calculate totals for all folders
            CalculateFolderTotals(rootFolder);

            var totalFolders = CountTotalFolders(rootFolder);
            var totalAlbumsInStructure = CountTotalAlbums(rootFolder);

            _logger.LogInformation("✅ SIMPLE UserAlbums approach complete!");
            _logger.LogInformation("📊 FINAL RESULTS:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbumsInStructure);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", rootFolder.TotalImageCount);
            _logger.LogInformation("   💾 Total size: {Size}", FormatBytes(rootFolder.TotalEstimatedSizeBytes));

            progressCallback?.Invoke(albumCount, $"Complete! Found {albumCount} albums");

            return rootFolder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build folder structure from UserAlbums (Simple)");
            progressCallback?.Invoke(0, $"Error: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Build folder structure from UserAlbums URI - gets ALL user albums directly
    /// This approach bypasses folder hierarchy and gets all albums in one go
    /// Useful when folder structure APIs are not working properly
    /// </summary>
    private async Task<FolderNode> BuildFolderStructureFromUserAlbumsAsync(string userAlbumsUri, string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🚀 Building folder structure from UserAlbums URI: {UserAlbumsUri}", userAlbumsUri);
        _logger.LogInformation("🎯 Goal: Get ALL user albums directly and organize them by folder path");

        try
        {
            // Create root folder structure
            var rootFolder = new FolderNode
            {
                NodeId = "useralbums-root",
                Name = $"{userNickname}'s Photos",
                Description = "Root folder from UserAlbums",
                Type = "Folder",
                FullPath = "",
                DateCreated = DateTime.Now,
                DateModified = DateTime.Now,
                HasChildren = true
            };

            var folderMap = new Dictionary<string, FolderNode>();
            folderMap[""] = rootFolder; // Root folder with empty path

            var albumCount = 0;
            var totalImages = 0;
            var totalSize = 0L;

            _logger.LogInformation("📡 Fetching ALL albums from UserAlbums endpoint...");

            // Get all albums from the UserAlbums endpoint
            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(userAlbumsUri, cancellationToken))
            {
                albumCount++;

                if (albumCount % 100 == 0)
                {
                    _logger.LogInformation("📊 Progress: {AlbumCount} albums processed...", albumCount);
                }

                try
                {
                    // Create album info
                    var albumInfo = new AlbumInfo
                    {
                        AlbumKey = album.AlbumKey,
                        NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                        Name = album.Name,
                        Description = album.Description ?? "",
                        UrlName = album.UrlName ?? "",
                        FullPath = album.UrlName ?? album.Name, // Use UrlName as path since UrlPath doesn't exist
                        ImageCount = album.ImageCount ?? 0,
                        EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                        DateCreated = album.Date ?? DateTime.MinValue,
                        DateModified = album.LastUpdated ?? DateTime.MinValue,
                        AllowDownloads = album.AllowDownloads ?? true,
                        Privacy = album.Privacy ?? "Unknown",
                        IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                    };

                    totalImages += albumInfo.ImageCount;
                    totalSize += albumInfo.EstimatedSizeBytes;

                    // Determine folder path from album's UrlPath
                    var folderPath = GetFolderPathFromAlbumPath(albumInfo.FullPath);

                    // Ensure folder exists in our structure
                    var targetFolder = EnsureFolderExists(folderMap, folderPath, userNickname);

                    // Add album to the appropriate folder
                    targetFolder.Albums.Add(albumInfo);

                    _logger.LogDebug("📸 Added album: {AlbumName} to folder: {FolderPath} ({ImageCount} images)",
                        album.Name, folderPath, album.ImageCount);
                }
                catch (Exception albumEx)
                {
                    _logger.LogWarning(albumEx, "❌ Failed to process album: {AlbumName}", album.Name);
                    // Continue with other albums
                }
            }

            // Calculate totals for all folders
            CalculateFolderTotals(rootFolder);

            var totalFolders = CountTotalFolders(rootFolder);
            var totalAlbumsInStructure = CountTotalAlbums(rootFolder);

            _logger.LogInformation("✅ UserAlbums approach complete!");
            _logger.LogInformation("📊 RESULTS from UserAlbums:");
            _logger.LogInformation("   📁 Total folders: {FolderCount}", totalFolders);
            _logger.LogInformation("   📸 Total albums: {AlbumCount}", totalAlbumsInStructure);
            _logger.LogInformation("   🖼️ Total images: {ImageCount}", rootFolder.TotalImageCount);
            _logger.LogInformation("   💾 Estimated size: {Size}", FormatBytes(rootFolder.TotalEstimatedSizeBytes));

            return rootFolder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build folder structure from UserAlbums");
            throw;
        }
    }

    /// <summary>
    /// Extract folder path from album's full path
    /// E.g., "SmugMug/Travel/Europe/Paris" -> "SmugMug/Travel/Europe"
    /// </summary>
    private string GetFolderPathFromAlbumPath(string albumPath)
    {
        if (string.IsNullOrEmpty(albumPath))
            return "";

        var lastSlashIndex = albumPath.LastIndexOf('/');
        return lastSlashIndex > 0 ? albumPath.Substring(0, lastSlashIndex) : "";
    }

    /// <summary>
    /// Ensure a folder exists in the folder map, creating parent folders as needed
    /// </summary>
    private FolderNode EnsureFolderExists(Dictionary<string, FolderNode> folderMap, string folderPath, string userNickname)
    {
        if (string.IsNullOrEmpty(folderPath))
        {
            return folderMap[""]; // Return root folder
        }

        if (folderMap.TryGetValue(folderPath, out var existingFolder))
        {
            return existingFolder;
        }

        // Create the folder and ensure its parent exists
        var parentPath = GetParentPath(folderPath);
        var parentFolder = EnsureFolderExists(folderMap, parentPath, userNickname);

        var folderName = folderPath.Contains('/') ? folderPath.Substring(folderPath.LastIndexOf('/') + 1) : folderPath;

        var newFolder = new FolderNode
        {
            NodeId = $"folder-{folderPath.Replace('/', '-')}",
            Name = folderName,
            Description = $"Folder at {folderPath}",
            Type = "Folder",
            FullPath = folderPath,
            DateCreated = DateTime.Now,
            DateModified = DateTime.Now,
            HasChildren = false // Will be updated if children are added
        };

        folderMap[folderPath] = newFolder;
        parentFolder.Children.Add(newFolder);
        parentFolder.HasChildren = true;

        _logger.LogDebug("📁 Created folder: {FolderName} at path: {FolderPath}", folderName, folderPath);

        return newFolder;
    }

    /// <summary>
    /// Discover albums for a specific folder using its URI
    /// </summary>
    private async Task DiscoverAlbumsForFolderAsync(FolderNode folderNode, string folderUri, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("🔍 Discovering albums for folder: {FolderName} using URI: {FolderUri}", folderNode.Name, folderUri);

            // Try to get albums using the folder's album endpoint
            var albumsUri = folderUri.TrimEnd('/') + "!albums";

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(albumsUri, cancellationToken))
            {
                var albumInfo = new AlbumInfo
                {
                    AlbumKey = album.AlbumKey,
                    NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                    Name = album.Name,
                    Description = album.Description ?? "",
                    UrlName = album.UrlName ?? "",
                    FullPath = string.IsNullOrEmpty(folderNode.FullPath) ? $"/{album.Name}" : $"{folderNode.FullPath}/{album.Name}",
                    ImageCount = album.ImageCount ?? 0,
                    EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                    DateCreated = album.Date ?? DateTime.MinValue,
                    DateModified = album.LastUpdated ?? DateTime.MinValue,
                    AllowDownloads = album.AllowDownloads ?? true,
                    Privacy = album.Privacy ?? "Unknown",
                    IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                };

                folderNode.Albums.Add(albumInfo);
                _logger.LogDebug("📸 Added album: {AlbumName} with {ImageCount} images", album.Name, album.ImageCount);
            }

            if (folderNode.Albums.Count > 0)
            {
                _logger.LogInformation("✅ Found {AlbumCount} albums in folder: {FolderName}", folderNode.Albums.Count, folderNode.Name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to discover albums for folder {FolderName} (this is normal for some folders)", folderNode.Name);
        }
    }

    /// <summary>
    /// Calculate totals for a folder and all its children (recursive)
    /// </summary>
    private void CalculateFolderTotals(FolderNode folder)
    {
        // Start with direct albums in this folder
        folder.TotalImageCount = folder.Albums.Sum(a => a.ImageCount);
        folder.TotalEstimatedSizeBytes = folder.Albums.Sum(a => a.EstimatedSizeBytes);

        // Recursively calculate for children and add to totals
        foreach (var child in folder.Children)
        {
            CalculateFolderTotals(child);
            folder.TotalImageCount += child.TotalImageCount;
            folder.TotalEstimatedSizeBytes += child.TotalEstimatedSizeBytes;
        }
    }

    /// <summary>
    /// Recursively build folder structure with COMPLETE traversal and comprehensive album discovery
    /// ENHANCED: Ensures ALL folders and albums are discovered at every level of the hierarchy
    /// </summary>
    private async Task BuildFolderStructureRecursivelyOptimizedAsync(FolderNode parentFolder, string nodeId, string parentPath, CancellationToken cancellationToken)
    {
        _logger.LogDebug("🔍 DEEP PROCESSING node: {NodeId} (path: {Path})", nodeId, parentPath);

        try
        {
            // Get ALL child nodes using comprehensive approach
            var childNodes = await GetChildNodesAsync(nodeId, cancellationToken);
            _logger.LogInformation("📊 Found {ChildCount} child nodes for {NodeId} at path: {Path}", childNodes.Count, nodeId, parentPath);

            // Process each child node comprehensively
            foreach (var childNode in childNodes)
            {
                var childPath = string.IsNullOrEmpty(parentPath) ? childNode.Name : $"{parentPath}/{childNode.Name}";
                _logger.LogDebug("🔄 Processing child: {ChildName} (Type: {Type}, NodeId: {NodeId})", childNode.Name, childNode.Type, childNode.NodeId);

                if (childNode.IsAlbum)
                {
                    // Process album with comprehensive metadata collection
                    _logger.LogInformation("📸 ALBUM DISCOVERED: {AlbumName} (Node: {NodeId}) at path: {Path}", childNode.Name, childNode.NodeId, childPath);

                    try
                    {
                        var albumInfo = await GetAlbumInfoAsync(childNode, childPath, cancellationToken);
                        parentFolder.Albums.Add(albumInfo);
                        parentFolder.TotalImageCount += albumInfo.ImageCount;
                        parentFolder.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;

                        _logger.LogInformation("✅ Album processed: {AlbumName} - {ImageCount} images, {Size}",
                            albumInfo.Name, albumInfo.ImageCount, FormatBytes(albumInfo.EstimatedSizeBytes));
                    }
                    catch (Exception albumEx)
                    {
                        _logger.LogError(albumEx, "❌ Failed to process album {AlbumName} (Node: {NodeId})", childNode.Name, childNode.NodeId);
                        // Continue with other albums even if one fails
                    }
                }
                else if (childNode.IsFolder)
                {
                    // Process subfolder with GUARANTEED recursion
                    _logger.LogInformation("📁 FOLDER DISCOVERED: {FolderName} (Node: {NodeId}) at path: {Path}", childNode.Name, childNode.NodeId, childPath);

                    var subFolder = new FolderNode
                    {
                        NodeId = childNode.NodeId,
                        Name = childNode.Name,
                        Description = childNode.Description ?? "",
                        Type = childNode.Type,
                        UrlName = childNode.UrlName ?? "",
                        FullPath = childPath,
                        DateCreated = childNode.DateAdded ?? DateTime.MinValue,
                        DateModified = childNode.DateModified ?? DateTime.MinValue,
                        HasChildren = childNode.HasChildren ?? false
                    };

                    // CRITICAL: ALWAYS recurse into folders regardless of HasChildren flag
                    // The HasChildren flag can be unreliable, so we must check every folder
                    _logger.LogDebug("🔄 Recursing into folder: {FolderName} (ignoring HasChildren={HasChildren})", childNode.Name, childNode.HasChildren);

                    try
                    {
                        await BuildFolderStructureRecursivelyOptimizedAsync(subFolder, childNode.NodeId, childPath, cancellationToken);

                        _logger.LogInformation("✅ Folder processed: {FolderName} - {AlbumCount} albums, {ImageCount} images, {Size}",
                            subFolder.Name, subFolder.Albums.Count, subFolder.TotalImageCount, FormatBytes(subFolder.TotalEstimatedSizeBytes));
                    }
                    catch (Exception folderEx)
                    {
                        _logger.LogError(folderEx, "❌ Failed to process folder {FolderName} (Node: {NodeId}), but adding it anyway", childNode.Name, childNode.NodeId);
                        // Continue with the folder even if recursion fails
                    }

                    parentFolder.Children.Add(subFolder);
                    parentFolder.TotalImageCount += subFolder.TotalImageCount;
                    parentFolder.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
                }
                else
                {
                    _logger.LogWarning("❓ Unknown node type: {Type} for {NodeName} (Node: {NodeId}) - investigating further", childNode.Type, childNode.Name, childNode.NodeId);

                    // Try to process unknown types as potential albums or folders
                    await TryProcessUnknownNodeTypeAsync(parentFolder, childNode, childPath, cancellationToken);
                }
            }

            _logger.LogInformation("🎯 Completed processing node {NodeId}: {AlbumCount} direct albums, {FolderCount} subfolders",
                nodeId, parentFolder.Albums.Count, parentFolder.Children.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to process node {NodeId} at path {Path}, continuing with other nodes", nodeId, parentPath);
            // Continue processing other nodes even if one fails
        }
    }

    /// <summary>
    /// Try to process unknown node types as potential albums or folders
    /// </summary>
    private async Task TryProcessUnknownNodeTypeAsync(FolderNode parentFolder, SmugMugNode unknownNode, string childPath, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("🔍 Investigating unknown node type: {Type} for {NodeName}", unknownNode.Type, unknownNode.Name);

            // Try to get more details about this node
            var nodeDetails = await GetNodeAsync(unknownNode.NodeId, cancellationToken);

            // Check if it has album-like properties
            if (nodeDetails.Uris?.Album?.Uri != null)
            {
                _logger.LogInformation("📸 Unknown node {NodeName} appears to be an album (has Album URI)", unknownNode.Name);
                try
                {
                    var albumInfo = await GetAlbumInfoAsync(unknownNode, childPath, cancellationToken);
                    parentFolder.Albums.Add(albumInfo);
                    parentFolder.TotalImageCount += albumInfo.ImageCount;
                    parentFolder.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
                }
                catch (Exception albumEx)
                {
                    _logger.LogWarning(albumEx, "Failed to process unknown node {NodeName} as album", unknownNode.Name);
                }
            }
            // Check if it might be a folder by trying to get children
            else
            {
                _logger.LogDebug("🔍 Checking if unknown node {NodeName} has children (might be a folder)", unknownNode.Name);
                try
                {
                    var children = await GetChildNodesAsync(unknownNode.NodeId, cancellationToken);
                    if (children.Count > 0)
                    {
                        _logger.LogInformation("📁 Unknown node {NodeName} appears to be a folder (has {ChildCount} children)", unknownNode.Name, children.Count);

                        var subFolder = new FolderNode
                        {
                            NodeId = unknownNode.NodeId,
                            Name = unknownNode.Name,
                            Description = unknownNode.Description ?? "",
                            Type = unknownNode.Type,
                            UrlName = unknownNode.UrlName ?? "",
                            FullPath = childPath,
                            DateCreated = unknownNode.DateAdded ?? DateTime.MinValue,
                            DateModified = unknownNode.DateModified ?? DateTime.MinValue,
                            HasChildren = children.Count > 0
                        };

                        // Recursively process this unknown node as a folder
                        await BuildFolderStructureRecursivelyOptimizedAsync(subFolder, unknownNode.NodeId, childPath, cancellationToken);

                        parentFolder.Children.Add(subFolder);
                        parentFolder.TotalImageCount += subFolder.TotalImageCount;
                        parentFolder.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
                    }
                }
                catch (Exception childEx)
                {
                    _logger.LogDebug(childEx, "Unknown node {NodeName} doesn't appear to have children", unknownNode.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to investigate unknown node type {Type} for {NodeName}", unknownNode.Type, unknownNode.Name);
        }
    }

    /// <summary>
    /// Try to add albums directly to a folder node with COMPREHENSIVE discovery
    /// ENHANCED: Uses multiple approaches to ensure no albums are missed
    /// </summary>
    private async Task TryAddDirectAlbumsToFolderAsync(FolderNode folderNode, string nodeId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🔍 COMPREHENSIVE album discovery for node: {NodeId} at path: {Path}", nodeId, folderNode.FullPath);

            var discoveredAlbums = 0;

            // Approach 1: Try to get albums using the !albums endpoint
            try
            {
                var albums = await GetAlbumsForNodeAsync(nodeId, cancellationToken);
                foreach (var album in albums)
                {
                    var albumInfo = await GetAlbumInfoAsync(album, folderNode.FullPath, cancellationToken);
                    folderNode.Albums.Add(albumInfo);
                    folderNode.TotalImageCount += albumInfo.ImageCount;
                    folderNode.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
                    discoveredAlbums++;
                }

                if (albums.Count > 0)
                {
                    _logger.LogInformation("✅ Approach 1: Found {AlbumCount} albums using !albums endpoint", albums.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Approach 1 failed for node {NodeId}", nodeId);
            }

            // Approach 2: Try alternative album discovery methods
            try
            {
                // Try to get albums using constructed URL patterns
                var alternativeUrls = new[]
                {
                    $"{BaseApiUrl}/node/{nodeId}!albumlist",
                    $"{BaseApiUrl}/node/{nodeId}/albums"
                };

                foreach (var url in alternativeUrls)
                {
                    try
                    {
                        _logger.LogDebug("🔍 Trying alternative album URL: {Uri}", url);

                        await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(url, cancellationToken))
                        {
                            // Convert SmugMugAlbum to AlbumInfo
                            var albumInfo = new AlbumInfo
                            {
                                AlbumKey = album.AlbumKey,
                                NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                                Name = album.Name,
                                Description = album.Description ?? "",
                                UrlName = album.UrlName ?? "",
                                FullPath = string.IsNullOrEmpty(folderNode.FullPath) ? $"/{album.Name}" : $"{folderNode.FullPath}/{album.Name}",
                                ImageCount = album.ImageCount ?? 0,
                                EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                                DateCreated = album.Date ?? DateTime.MinValue,
                                DateModified = album.LastUpdated ?? DateTime.MinValue,
                                AllowDownloads = album.AllowDownloads ?? true,
                                Privacy = album.Privacy ?? "Unknown",
                                IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                            };

                            folderNode.Albums.Add(albumInfo);
                            folderNode.TotalImageCount += albumInfo.ImageCount;
                            folderNode.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
                            discoveredAlbums++;
                        }

                        if (discoveredAlbums > 0)
                        {
                            _logger.LogInformation("✅ Approach 2: Found {AlbumCount} albums using URL: {Url}", discoveredAlbums, url);
                            break; // Stop trying other URLs if we found albums
                        }
                    }
                    catch (Exception urlEx)
                    {
                        _logger.LogDebug(urlEx, "Alternative URL {Url} failed", url);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Approach 2 failed for node {NodeId}", nodeId);
            }

            if (discoveredAlbums > 0)
            {
                _logger.LogInformation("✅ TOTAL: Found {AlbumCount} albums for node {NodeId}", discoveredAlbums, nodeId);
            }
            else
            {
                _logger.LogDebug("No albums found for node {NodeId} (this is normal for pure folder nodes)", nodeId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Album discovery failed for node {NodeId} (this is normal for some node types)", nodeId);
            // This is not an error - some nodes simply don't have albums
        }
    }

    /// <summary>
    /// Get albums for a specific node using various API approaches
    /// </summary>
    private async Task<List<SmugMugNode>> GetAlbumsForNodeAsync(string nodeId, CancellationToken cancellationToken)
    {
        var albums = new List<SmugMugNode>();

        try
        {
            // Try to get albums using the node's album endpoint
            var albumsUrl = $"{BaseApiUrl}/node/{nodeId}!albums";

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(albumsUrl, cancellationToken))
            {
                if (node.IsAlbum)
                {
                    albums.Add(node);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get albums for node {NodeId} using !albums endpoint", nodeId);
        }

        return albums;
    }

    /// <summary>
    /// Create virtual root with all user albums (fallback approach)
    /// </summary>
    private async Task<FolderNode> CreateVirtualRootWithAllAlbumsAsync(string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 Creating virtual root with all user albums for: {UserNickname}", userNickname);

        var virtualRoot = new FolderNode
        {
            NodeId = "virtual-root",
            Name = $"{userNickname}'s Photos",
            Description = "Virtual root containing all accessible albums",
            Type = "Folder",
            FullPath = "",
            DateCreated = DateTime.Now,
            DateModified = DateTime.Now,
            HasChildren = true
        };

        try
        {
            // Use cached UserAlbums URI if available
            var albums = await GetAllUserAlbumsAsync(cancellationToken);

            _logger.LogInformation("✅ Retrieved {AlbumCount} albums for virtual root", albums.Count);

            // Convert SmugMugAlbum objects to AlbumInfo objects
            foreach (var album in albums)
            {
                var albumInfo = new AlbumInfo
                {
                    AlbumKey = album.AlbumKey,
                    NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                    Name = album.Name,
                    Description = album.Description ?? "",
                    UrlName = album.UrlName ?? "",
                    FullPath = $"/{album.Name}",
                    ImageCount = album.ImageCount ?? 0,
                    EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                    DateCreated = album.Date ?? DateTime.MinValue,
                    DateModified = album.LastUpdated ?? DateTime.MinValue,
                    AllowDownloads = album.AllowDownloads ?? true,
                    Privacy = album.Privacy ?? "Unknown",
                    IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                };

                virtualRoot.Albums.Add(albumInfo);
                virtualRoot.TotalImageCount += albumInfo.ImageCount;
                virtualRoot.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }

            _logger.LogInformation("✅ Virtual root created with {AlbumCount} albums, {ImageCount} total images",
                virtualRoot.Albums.Count, virtualRoot.TotalImageCount);

            return virtualRoot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to create virtual root with all albums");
            throw new InvalidOperationException($"Failed to create virtual root: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Get folder structure using cached Folder URI for efficient access
    /// </summary>
    private async Task<FolderNode> GetFolderStructureUsingCachedFolderUriAsync(string folderUri, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🗂️ Using cached Folder URI for efficient folder structure access: {Uri}", folderUri);

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugFolder>>(HttpMethod.Get, folderUri, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get folder structure from cached URI: {folderUri}");
        }

        var rootFolder = response.Response;
        _logger.LogInformation("✅ Successfully retrieved root folder from cached URI: {FolderName}", rootFolder.Name);

        // Convert SmugMugFolder to FolderNode with detailed information
        var folderNode = new FolderNode
        {
            NodeId = rootFolder.NodeId ?? "cached-root",
            Name = rootFolder.Name ?? "Root Folder",
            Description = rootFolder.Description ?? "Root folder from cached Folder URI",
            Type = "Folder",
            UrlName = rootFolder.UrlName ?? "",
            FullPath = "",
            DateCreated = rootFolder.DateAdded ?? DateTime.MinValue,
            DateModified = rootFolder.DateModified ?? DateTime.MinValue,
            HasChildren = rootFolder.HasChildren ?? false
        };

        // If the folder has children, recursively build the structure
        if (rootFolder.HasChildren == true && !string.IsNullOrEmpty(rootFolder.NodeId))
        {
            _logger.LogInformation("📁 Root folder has children, building recursive structure...");
            await BuildFolderStructureRecursivelyOptimizedAsync(folderNode, rootFolder.NodeId, "", cancellationToken);
        }

        _logger.LogInformation("🎯 Cached folder structure complete - Total: {AlbumCount} albums, {ImageCount} images, {Size} estimated size",
            folderNode.Albums.Count, folderNode.TotalImageCount, FormatBytes(folderNode.TotalEstimatedSizeBytes));

        return folderNode;
    }



    /// <summary>
    /// Get album information from a node using proper URI navigation
    /// </summary>
    private async Task<AlbumInfo> GetAlbumInfoAsync(SmugMugNode albumNode, string parentPath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting album info for node {NodeId} ({Name})", albumNode.NodeId, albumNode.Name);

            // CRITICAL: Use the Album URI from the node response for proper navigation
            if (albumNode.Uris?.Album?.Uri != null)
            {
                _logger.LogDebug("Using Album URI from node: {AlbumUri}", albumNode.Uris.Album.Uri);
                var albumResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, albumNode.Uris.Album.Uri, cancellationToken);

                if (albumResponse?.Response != null)
                {
                    var album = albumResponse.Response;
                    _logger.LogDebug("Successfully retrieved album details: {AlbumName} with {ImageCount} images", album.Name, album.ImageCount);

                    return new AlbumInfo
                    {
                        AlbumKey = album.AlbumKey,
                        NodeId = albumNode.NodeId,
                        Name = album.Name,
                        Description = album.Description ?? "",
                        UrlName = album.UrlName ?? "",
                        FullPath = $"{parentPath}/{album.Name}",
                        ImageCount = album.ImageCount ?? 0,
                        EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                        DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                        DateModified = album.LastUpdated ?? DateTime.MinValue,
                        AllowDownloads = album.AllowDownloads ?? true,
                        Privacy = album.Privacy ?? "Unknown",
                        IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                    };
                }
                else
                {
                    _logger.LogWarning("Album URI returned null response for node {NodeId}", albumNode.NodeId);
                }
            }
            else
            {
                _logger.LogWarning("No Album URI found in node {NodeId} - this may indicate the node is not actually an album", albumNode.NodeId);
            }

            // Fallback: create album info from node data only
            _logger.LogDebug("Creating fallback album info from node data for {NodeId}", albumNode.NodeId);

            // Try to extract album key from Album URI even if we can't get full album details
            var albumKey = "";
            if (albumNode.Uris?.Album?.Uri != null)
            {
                var albumUri = albumNode.Uris.Album.Uri;
                albumKey = albumUri.Split('/').LastOrDefault() ?? "";
                _logger.LogDebug("Extracted album key from URI: {AlbumKey}", albumKey);
            }

            return new AlbumInfo
            {
                AlbumKey = albumKey, // Extracted from URI if available
                NodeId = albumNode.NodeId,
                Name = albumNode.Name,
                Description = albumNode.Description ?? "",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name}",
                ImageCount = 0, // Will need to be fetched separately
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = true, // Default assumption
                Privacy = albumNode.Privacy ?? "Unknown",
                IsPublic = albumNode.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get album details for node {NodeId}, creating minimal album info", albumNode.NodeId);

            // Even in error case, try to extract album key from URI if available
            var albumKey = "";
            try
            {
                if (albumNode.Uris?.Album?.Uri != null)
                {
                    var albumUri = albumNode.Uris.Album.Uri;
                    albumKey = albumUri.Split('/').LastOrDefault() ?? "";
                    _logger.LogDebug("Extracted album key from URI in error case: {AlbumKey}", albumKey);
                }
            }
            catch (Exception keyEx)
            {
                _logger.LogDebug(keyEx, "Failed to extract album key from URI in error case");
            }

            // Return minimal album info if everything fails
            return new AlbumInfo
            {
                AlbumKey = albumKey, // Try to preserve album key even in error case
                NodeId = albumNode.NodeId,
                Name = albumNode.Name ?? "Unknown Album",
                Description = "Failed to load album details",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name ?? "Unknown"}",
                ImageCount = 0,
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = false, // Conservative default when we can't get details
                Privacy = "Unknown",
                IsPublic = false
            };
        }
    }





    /// <summary>
    /// Estimate album size based on image count (rough approximation)
    /// </summary>
    private static long EstimateAlbumSize(int imageCount)
    {
        // Rough estimate: 5MB per image on average
        return imageCount * 5L * 1024 * 1024;
    }

    /// <summary>
    /// Verifies that we have the correct access level for private data
    /// </summary>
    public Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var user = Task.Run(async () => await GetAuthenticatedUserAsync(cancellationToken)).GetAwaiter().GetResult();
            return Task.FromResult(!string.IsNullOrEmpty(user.NickName));
        }
        catch
        {
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Gets detailed information about the user's access level and permissions
    /// </summary>
    public async Task<AccessLevelInfo> GetAccessLevelInfoAsync(CancellationToken cancellationToken = default)
    {
        var accessInfo = new AccessLevelInfo();
        _logger.LogInformation("🔍 DETAILED ACCESS LEVEL ANALYSIS STARTING...");

        try
        {
            _logger.LogInformation("Step 1: Getting authenticated user information...");
            var user = Task.Run(async () => await GetAuthenticatedUserAsync(cancellationToken)).GetAwaiter().GetResult();
            accessInfo.HasUserAccess = true;
            accessInfo.UserNickname = user.NickName;
            _logger.LogInformation("✅ User access confirmed: {UserName} ({NickName})", user.Name, user.NickName);

            // Check if user has Node URI (critical for folder access)
            if (user.Uris?.Node?.Uri != null)
            {
                _logger.LogInformation("✅ Node URI found: {NodeUri}", user.Uris.Node.Uri);
                accessInfo.HasPrivateAccess = true;
            }
            else
            {
                _logger.LogWarning("❌ NO NODE URI FOUND - This indicates LIMITED ACCESS");
                _logger.LogWarning("   This means you cannot access your folder structure");
                _logger.LogWarning("   Possible causes:");
                _logger.LogWarning("   - OAuth permissions were not granted properly");
                _logger.LogWarning("   - Different authentication session than browser");
                _logger.LogWarning("   - SmugMug account restrictions");
                accessInfo.HasPrivateAccess = false;
            }

            try
            {
                _logger.LogInformation("Step 2: Attempting to access root node...");
                var rootNode = await GetUserRootNodeAsync(cancellationToken);
                accessInfo.HasNodeAccess = true;
                accessInfo.RootNodeId = rootNode.NodeId;
                _logger.LogInformation("✅ Root node access confirmed: {NodeId}", rootNode.NodeId);
            }
            catch (Exception nodeEx)
            {
                _logger.LogWarning("❌ Root node access failed: {Error}", nodeEx.Message);
                accessInfo.HasNodeAccess = false;
            }

            // Determine overall access level
            if (accessInfo.HasPrivateAccess && accessInfo.HasNodeAccess)
            {
                accessInfo.AccessLevel = "Full";
                accessInfo.CanAccessPrivateContent = true;
                _logger.LogInformation("🎉 FULL ACCESS CONFIRMED - Can access private photos and folder structure");
            }
            else if (accessInfo.HasUserAccess)
            {
                accessInfo.AccessLevel = "Limited";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogWarning("⚠️ LIMITED ACCESS DETECTED - Can only access public content");

                var recommendations = new List<string>();
                if (!accessInfo.HasPrivateAccess)
                {
                    recommendations.Add("Re-authenticate and ensure you grant full permissions");
                    recommendations.Add("Make sure to click 'Authorize' or 'Allow' when prompted");
                }
                if (!accessInfo.HasNodeAccess)
                {
                    recommendations.Add("Cannot access folder structure - may need different OAuth scope");
                }
                accessInfo.Recommendations = recommendations.ToArray();
            }
            else
            {
                accessInfo.AccessLevel = "None";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogError("❌ NO ACCESS - Authentication failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Access level analysis failed");
            accessInfo.HasUserAccess = false;
            accessInfo.AccessLevel = "Unknown";
            accessInfo.CanAccessPrivateContent = false;
            accessInfo.Recommendations = new[] { "Authentication failed. Please try re-authenticating with SmugMug." };
        }

        _logger.LogInformation("🔍 ACCESS LEVEL ANALYSIS COMPLETE:");
        _logger.LogInformation("   Access Level: {AccessLevel}", accessInfo.AccessLevel);
        _logger.LogInformation("   Can Access Private Content: {CanAccess}", accessInfo.CanAccessPrivateContent);
        _logger.LogInformation("   Has User Access: {HasUser}", accessInfo.HasUserAccess);
        _logger.LogInformation("   Has Private Access: {HasPrivate}", accessInfo.HasPrivateAccess);
        _logger.LogInformation("   Has Node Access: {HasNode}", accessInfo.HasNodeAccess);

        return accessInfo;
    }

    /// <summary>
    /// Gets album information for a specific album
    /// </summary>
    public async Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get album: {albumKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets all images in a specific album
    /// </summary>
    public async Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting images for album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        var images = new List<SmugMugImage>();

        await foreach (var image in GetPagedResultsAsync<SmugMugImage>(url, cancellationToken))
        {
            images.Add(image);
        }

        _logger.LogDebug("Found {Count} images in album: {AlbumKey}", images.Count, albumKey);
        return images;
    }

    /// <summary>
    /// Gets the size details for a specific image
    /// </summary>
    public async Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting size details for image: {ImageKey}", imageKey);

        var url = $"{BaseApiUrl}/image/{imageKey}!sizes";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugImageSizes>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get image sizes: {imageKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Downloads image data from the specified URL
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        return await DownloadImageAsync(imageUrl, null, cancellationToken);
    }

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Downloading image from: {ImageUrl}", imageUrl);

        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, imageUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

        response.EnsureSuccessStatusCode();

        var contentLength = response.Content.Headers.ContentLength;
        var stream = await response.Content.ReadAsStreamAsync(cancellationToken);

        if (progress != null && contentLength.HasValue)
        {
            return new ProgressStream(stream, contentLength.Value, progress);
        }

        return stream;
    }

    /// <summary>
    /// Send an authenticated request to the SmugMug API
    /// </summary>
    private async Task<T?> SendAuthenticatedRequestAsync<T>(HttpMethod method, string url, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("🔍 Creating authenticated request for {Method} {Url}", method.Method, url);

            var request = _authService.CreateAuthenticatedRequest(method, url);
            request.Headers.Add("Accept", "application/json");

            _logger.LogDebug("📤 Sending {Method} request to: {Url}", method.Method, url);
            _logger.LogDebug("📋 Request headers: {Headers}",
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogInformation("📥 Response status: {StatusCode} {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("❌ API request failed: {StatusCode} {ReasonPhrase} - {Content}",
                    response.StatusCode, response.ReasonPhrase, errorContent);

                // Special handling for OAuth signature errors
                if (errorContent.Contains("oauth_problem=signature_invalid"))
                {
                    _logger.LogError("🚨 OAUTH SIGNATURE INVALID - This indicates an issue with OAuth signature generation");
                    _logger.LogError("   URL: {Url}", url);
                    _logger.LogError("   Method: {Method}", method.Method);
                    _logger.LogError("   This might be due to:");
                    _logger.LogError("   1. Incorrect OAuth parameter encoding");
                    _logger.LogError("   2. Wrong signature base string construction");
                    _logger.LogError("   3. Mismatched consumer secret or access token secret");
                    _logger.LogError("   4. Timestamp/nonce issues");
                }

                throw new HttpRequestException($"API request failed: {response.StatusCode} {response.ReasonPhrase}");
            }

            var contentString = await response.Content.ReadAsStringAsync(cancellationToken);

            // Enhanced logging for debugging user data issues
            if (url.Contains("!authuser") || url.Contains("!siteuser"))
            {
                _logger.LogDebug("Raw API response from {Url}:", url);
                _logger.LogDebug("Response content size: {Size} characters (JSON)", contentString.Length);
                _logger.LogDebug("Response content type: {ContentType}", response.Content.Headers.ContentType?.MediaType);
            }

            // Deserialize JSON response
            _logger.LogDebug("📦 Deserializing JSON response ({Size} characters)", contentString.Length);
            return System.Text.Json.JsonSerializer.Deserialize<T>(contentString, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "💥 Failed to send authenticated request to: {Url}", url);
            throw;
        }
    }

    /// <summary>
    /// Get paged results from a SmugMug API endpoint
    /// SmugMug returns collections in a specific format where items are properties of the response
    /// </summary>
    private async IAsyncEnumerable<T> GetPagedResultsAsync<T>(string baseUrl, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var start = 1;
        const int pageSize = 100;
        bool hasMore = true;

        while (hasMore && !cancellationToken.IsCancellationRequested)
        {
            var url = $"{baseUrl}?_start={start}&_count={pageSize}";
            _logger.LogDebug("Getting paged results from: {Url}", url);

            // Try different response formats that SmugMug might use
            List<T> items = new();
            SmugMugPagingInfo? pagingInfo = null;

            try
            {
                // First try: Direct array response (most common for collections)
                var directResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<T>>>(HttpMethod.Get, url, cancellationToken);
                if (directResponse?.Response != null && directResponse.Response.Any())
                {
                    items = directResponse.Response;
                    _logger.LogDebug("Successfully parsed direct array response with {ItemCount} items", items.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Direct array response parsing failed, trying collection response format");

                try
                {
                    // Second try: Collection response format
                    var collectionResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugCollectionResponse<T>>>(HttpMethod.Get, url, cancellationToken);
                    if (collectionResponse?.Response != null)
                    {
                        items = ExtractItemsFromCollectionResponse<T>(collectionResponse.Response);
                        pagingInfo = collectionResponse.Response.Pages;
                        _logger.LogDebug("Successfully parsed collection response with {ItemCount} items", items.Count);
                    }
                }
                catch (Exception ex2)
                {
                    _logger.LogError(ex2, "Both response format attempts failed for URL: {Url}", url);
                    hasMore = false;
                    yield break;
                }
            }

            if (!items.Any())
            {
                _logger.LogDebug("No items found in any response format");
                hasMore = false;
                yield break;
            }

            _logger.LogDebug("Found {ItemCount} items in page starting at {Start}", items.Count, start);

            foreach (var item in items)
            {
                yield return item;
            }

            // Check pagination info if available
            if (pagingInfo != null)
            {
                hasMore = pagingInfo.HasNextPage;
                if (hasMore)
                {
                    // Extract start parameter from next page URL if available
                    if (!string.IsNullOrEmpty(pagingInfo.NextPage))
                    {
                        // Parse the next page URL to get the start parameter
                        var nextPageUrl = pagingInfo.NextPage;
                        var startIndex = nextPageUrl.IndexOf("_start=");
                        if (startIndex >= 0)
                        {
                            var startStr = nextPageUrl.Substring(startIndex + 7);
                            var endIndex = startStr.IndexOf('&');
                            if (endIndex >= 0)
                            {
                                startStr = startStr.Substring(0, endIndex);
                            }

                            if (int.TryParse(startStr, out var nextStart))
                            {
                                start = nextStart;
                            }
                            else
                            {
                                start += pageSize;
                            }
                        }
                        else
                        {
                            start += pageSize;
                        }
                    }
                    else
                    {
                        start += pageSize;
                    }
                }
            }
            else
            {
                // Fallback: check if we got a full page
                hasMore = items.Count == pageSize;
                start += pageSize;
            }
        }
    }

    /// <summary>
    /// Extract items from SmugMug collection response based on the type
    /// SmugMug returns collections with items as properties named after the type (plural)
    /// </summary>
    private List<T> ExtractItemsFromCollectionResponse<T>(SmugMugCollectionResponse<T> collectionResponse)
    {
        var items = new List<T>();
        var typeName = typeof(T).Name;

        try
        {
            // First check if Items property is populated (this is our fallback)
            if (collectionResponse.Items.Any())
            {
                items.AddRange(collectionResponse.Items);
                _logger.LogDebug("Found {ItemCount} items in Items property for type {TypeName}", items.Count, typeName);
                return items;
            }

            // SmugMug returns different collection types with items as properties
            // For collections, SmugMug typically returns multiple items, not single items
            // The single item properties (Node, Album, Image) are for individual item responses
            switch (typeName)
            {
                case "SmugMugNode":
                    // For node collections, check if there's a single Node (shouldn't happen for children)
                    if (collectionResponse.Node != null)
                    {
                        items.Add((T)(object)collectionResponse.Node);
                        _logger.LogDebug("Found single Node in collection response (unusual for children endpoint)");
                    }
                    break;

                case "SmugMugAlbum":
                    // For album collections, check if there's a single Album
                    if (collectionResponse.Album != null)
                    {
                        items.Add((T)(object)collectionResponse.Album);
                        _logger.LogDebug("Found single Album in collection response");
                    }
                    break;

                case "SmugMugImage":
                    // For image collections, check if there's a single Image
                    if (collectionResponse.Image != null)
                    {
                        items.Add((T)(object)collectionResponse.Image);
                        _logger.LogDebug("Found single Image in collection response");
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown collection type: {TypeName}", typeName);
                    break;
            }

            _logger.LogDebug("Extracted {ItemCount} items from collection response for type {TypeName}", items.Count, typeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract items from collection response for type: {TypeName}", typeName);
        }

        return items;
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
