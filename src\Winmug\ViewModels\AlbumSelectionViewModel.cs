using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Winmug.Core.Models;
using Winmug.Core.Services;

namespace Winmug.ViewModels;

/// <summary>
/// ViewModel for the album selection window
/// </summary>
public partial class AlbumSelectionViewModel : ObservableObject
{
    private readonly ISmugMugApiClient _apiClient;
    private readonly ILogger _logger;

    [ObservableProperty]
    private ObservableCollection<SelectableAlbum> _albums = new();

    [ObservableProperty]
    private ICollectionView? _albumsView;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private bool _hideAlbums;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private int _totalAlbumCount;

    [ObservableProperty]
    private int _selectedAlbumCount;

    [ObservableProperty]
    private long _totalSelectedSizeBytes;

    [ObservableProperty]
    private int _totalSelectedImageCount;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    public AlbumSelectionViewModel(ISmugMugApiClient apiClient, ILogger logger)
    {
        _apiClient = apiClient;
        _logger = logger;
        
        // Set up collection view for filtering
        AlbumsView = CollectionViewSource.GetDefaultView(Albums);
        AlbumsView.Filter = FilterAlbums;
        
        // Subscribe to property changes for real-time updates
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// Human-readable total selected size
    /// </summary>
    public string TotalSelectedSize => FormatBytes(TotalSelectedSizeBytes);

    /// <summary>
    /// Load all albums from SmugMug
    /// </summary>
    [RelayCommand]
    private async Task LoadAlbumsAsync()
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            StatusMessage = "Loading albums...";
            Albums.Clear();

            // Get folder structure to find all albums using async method
            var folderStructure = await _apiClient.GetFolderStructureAsync();
            var allAlbums = new List<SelectableAlbum>();

            // Recursively collect all albums from folder structure
            CollectAlbumsFromFolder(folderStructure, allAlbums);

            // Add albums to collection
            foreach (var album in allAlbums.OrderBy(a => a.Name))
            {
                Albums.Add(album);

                // Subscribe to selection changes
                album.PropertyChanged += OnAlbumSelectionChanged;
            }

            TotalAlbumCount = Albums.Count;
            StatusMessage = $"Loaded {TotalAlbumCount} albums";

            // Refresh the view
            AlbumsView?.Refresh();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load albums");
            StatusMessage = $"Failed to load albums: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// Select all visible albums
    /// </summary>
    [RelayCommand]
    private void SelectAll()
    {
        var albumsToSelect = AlbumsView?.Cast<SelectableAlbum>() ?? Albums;
        foreach (var album in albumsToSelect)
        {
            album.IsSelected = true;
        }
    }

    /// <summary>
    /// Deselect all albums
    /// </summary>
    [RelayCommand]
    private void DeselectAll()
    {
        foreach (var album in Albums)
        {
            album.IsSelected = false;
        }
    }

    /// <summary>
    /// Apply search filter
    /// </summary>
    [RelayCommand]
    private void ApplyFilter()
    {
        AlbumsView?.Refresh();
    }

    /// <summary>
    /// Get list of selected albums
    /// </summary>
    public List<SelectableAlbum> GetSelectedAlbums()
    {
        return Albums.Where(a => a.IsSelected).ToList();
    }

    /// <summary>
    /// Filter albums based on search text and hide albums setting
    /// </summary>
    private bool FilterAlbums(object item)
    {
        if (item is not SelectableAlbum album) return false;

        // Apply hide albums filter (if needed - this could be extended)
        if (HideAlbums)
        {
            // Could implement logic to hide certain types of albums
            // For now, just return true
        }

        // Apply search filter
        if (!string.IsNullOrEmpty(SearchText))
        {
            return album.DisplayName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                   (!string.IsNullOrEmpty(album.Description) && 
                    album.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
        }

        return true;
    }

    /// <summary>
    /// Recursively collect albums from folder structure
    /// </summary>
    private void CollectAlbumsFromFolder(FolderNode folder, List<SelectableAlbum> albums)
    {
        // Add albums from this folder
        foreach (var albumInfo in folder.Albums)
        {
            var selectableAlbum = new SelectableAlbum
            {
                AlbumKey = albumInfo.AlbumKey,
                Name = albumInfo.Name,
                Description = albumInfo.Description,
                UrlName = albumInfo.UrlName,
                ImageCount = albumInfo.ImageCount,
                EstimatedSizeBytes = albumInfo.EstimatedSizeBytes,
                DateCreated = albumInfo.DateCreated,
                DateModified = albumInfo.DateModified,
                Privacy = albumInfo.Privacy,
                NodeId = albumInfo.NodeId,
                ParentNodeId = albumInfo.ParentNodeId,
                FullPath = albumInfo.FullPath,
                AllowDownloads = albumInfo.AllowDownloads,
                IsPrivate = !albumInfo.IsPublic,
                IsPasswordProtected = !string.IsNullOrEmpty(albumInfo.Privacy) && albumInfo.Privacy.Contains("Password")
            };
            
            albums.Add(selectableAlbum);
        }

        // Recursively process child folders
        foreach (var childFolder in folder.Children)
        {
            CollectAlbumsFromFolder(childFolder, albums);
        }
    }

    /// <summary>
    /// Handle album selection changes
    /// </summary>
    private void OnAlbumSelectionChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SelectableAlbum.IsSelected))
        {
            UpdateSelectionStatistics();
        }
    }

    /// <summary>
    /// Handle property changes
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SearchText) || e.PropertyName == nameof(HideAlbums))
        {
            AlbumsView?.Refresh();
        }
    }

    /// <summary>
    /// Update selection statistics
    /// </summary>
    private void UpdateSelectionStatistics()
    {
        var selectedAlbums = Albums.Where(a => a.IsSelected).ToList();
        
        SelectedAlbumCount = selectedAlbums.Count;
        TotalSelectedSizeBytes = selectedAlbums.Sum(a => a.EstimatedSizeBytes);
        TotalSelectedImageCount = selectedAlbums.Sum(a => a.ImageCount);
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
